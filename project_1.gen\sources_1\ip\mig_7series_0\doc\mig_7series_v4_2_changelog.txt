2021.1:
 * Version 4.2 (Rev. 1)
 * No changes

2020.3:
 * Version 4.2 (Rev. 1)
 * No changes

2020.2.2:
 * Version 4.2 (Rev. 1)
 * No changes

2020.2.1:
 * Version 4.2 (Rev. 1)
 * No changes

2020.2:
 * Version 4.2 (Rev. 1)
 * No changes

2020.1.1:
 * Version 4.2 (Rev. 1)
 * No changes

2020.1:
 * Version 4.2 (Rev. 1)
 * No changes

2019.2.2:
 * Version 4.2 (Rev. 1)
 * No changes

2019.2.1:
 * Version 4.2 (Rev. 1)
 * No changes

2019.2:
 * Version 4.2 (Rev. 1)
 * No changes

2019.1.3:
 * Version 4.2 (Rev. 1)
 * No changes

2019.1.2:
 * Version 4.2 (Rev. 1)
 * No changes

2019.1.1:
 * Version 4.2 (Rev. 1)
 * No changes

2019.1:
 * Version 4.2 (Rev. 1)
 * General: Vivado 2019.1 software support.

2018.3.1:
 * Version 4.2
 * No changes

2018.3:
 * Version 4.2
 * Bug Fix: Resolved Windows 10 GUI crashes when trying to broswe to a XDC/UCF file (Xilinx Answer 67168).
 * Other: Vivado 2018.3 software support.
 * Other: Updated 7-Series MIG GUI for improved Windows 10 support.

2018.2:
 * Version 4.1 (Rev. 1)
 * General: Vivado 2018.2 software support.

2018.1:
 * Version 4.1
 * General: Vivado 2018.1 software support.

2017.4:
 * Version 4.0 (Rev. 6)
 * General: Vivado 2017.4 software support.

2017.3:
 * Version 4.0 (Rev. 5)
 * General: Vivado 2017.3 software support.
 * General: Added support for CPG236 packaged FPGA

2017.2:
 * Version 4.0 (Rev. 4)
 * General: Vivado 2017.2 software support.

2017.1:
 * Version 4.0 (Rev. 3)
 * General: Vivado 2017.1 software support.

2016.4:
 * Version 4.0 (Rev. 2)
 * General: Vivado 2016.4 software support.

2016.3:
 * Version 4.0 (Rev. 1)
 * New Feature: Added GUI option in Memory Controller Options page for number of Bank Machines selection.
 * Other: Beta Support for Spartan-7 devices.

2016.2:
 * Version 4.0
 * Updated the Maximum supported Frequency values as per the DS 181, DS 182, DS183, DS187 and DS191.

2016.1:
 * Version 3.0
 * Support for RLDRAM II, QDRII+ SRAM and RLDRAM 3 designs on Zynq Kintex-7 Architectured devices.

2015.4.2:
 * Version 2.4 (Rev. 1)
 * No changes

2015.4.1:
 * Version 2.4 (Rev. 1)
 * No changes

2015.4:
 * Version 2.4 (Rev. 1)
 * Vivado 2015.4 software support.

2015.3:
 * Version 2.4
 * RLDRAM II and QDRII+ SRAM write calibration and complex read calibration enhancements.
 * RLDRAM III complex read calibration enhancements.

2015.2.1:
 * Version 2.3 (Rev. 2)
 * No changes

2015.2:
 * Version 2.3 (Rev. 2)
 * Added Address Mirroring support for DDR3 Dual Rank UDIMMs.
 * Resolved DDR3 AXI, ECC enabled design failures. See (Xilinx Answer 64421) for details.

2015.1:
 * Version 2.3 (Rev. 1)
 * Enhanced RLDRAM II and RLDRAM 3 controller efficiency.
 * Resolved DDR2/DDR3 issue where additional BUFG added in "opt_design" for the "freq_refclk" can lead to minimum pulse width timing violations. See (Xilinx Answer 63165) for details.
 * Resolved issue with Virtex-7 HT where error is generated when trying to open the MIG 7 Series tool when targeting a part with an flg package - Failed to generate custom UI outputs.  See (Xilinx Answer 60527) for details.
 * Resolved issues with DDR3/DDR2 where Manual Window Check feature does not work with VIO 2.0.  See (Xilinx Answer 59284) for details.
 * Resolved issue with the "No Buffer" option always expecting a 200MHz on clk_ref_i and instantiates additional MMCM for 300 or 400MHz. See (Xilinx Answer 63227) for details.
 * Resolved issue with CRITICAL WARNING message when multiple MIG IP are added to the same project.  See (Xilinx Answer 58621) for details.

2014.4.1:
 * Version 2.3
 * No changes

2014.4:
 * Version 2.3
 * Updated maximum frequencies and controller rates as per specifications listed in 7 Series and Zync DC and Switching Characteristics Datasheets
 * DDR3 write calibration changes

2014.3:
 * Version 2.2
 * DDR3 SDRAM, DDR2 SDRAM, RLDRAM II, LPDDR2 SDRAM, QDRIIPLUS SRAM max supported frequencies updated. See (Xilinx Answer 61853) for details."
 * Resolved (Xilinx Answer 61744) "MIG 7 Series DDR3 - ECC Multiple errors are seen in hardware when targeting Vivado 2014.2.  Errors were not seen in previous versions."
 * Resolved (Xilinx Answer 61521) "MIG 7 Series - Cannot generate data width greater than 8-bits for CPG325 packages"
 * Resolved (Xilinx Answer 60480) "MIG 7 Series - Receiving ERROR [Drc 23-20] when CLOCK_DEDICATED_ROUTE set to BACKBONE but backbone resources are not used"
 * Resolved (Xilinx Answer 60051) "MIG 7 Series DDR3 - VCS simulations fail with unresolved modules"

2014.2:
 * Version 2.1
 * DDR3 clocking and read path calibration updates. Refer to Answer Record 60470 for details
 * Addition of Artix-7Q(xq7a50t-cs325,xq7a50t-fg484) and XAZynq (xa7z030-fbg484) devices

2014.1:
 * Version 2.0 (Rev. 3)
 * Extended IES and VCS support to Multi-Controller and Multi-Interface designs

2013.4:
 * Version 2.0 (Rev. 2)
 * Added OOC support
 * Added support for IES and VCS Simulators

2013.3:
 * Version 2.0 (Rev. 1)
 * Added support for ILA 3.0 and VIO 3.0
 * Resolved controller hang issues on read-modify-write commands (See Xilinx Answer 54710)
 * Resolved Clock Driver Enable settings for RC1 on RDIMM interfaces (See Xilinx Answer 57279)
 * Updated Chipscope debug signals for OCLKDELAY calibration (See Xilinx Answer 54918)
 * Resolved timing failures with larger SSI devices  (See Xilinx Answer 56385)
 * Added AXI addressing support over 32 bits for DDR2 and DDR3
 * Corrected Chip Select width for single rank RDIMM devices (See Xilinx Answer 57436)

2013.2:
 * Version 2.0
 * 2013.2 software support
 * Added support for ILA 2.0 and VIO 2.0

2013.1:
 * Version 1.9.a
 * 2013.1 software support
 * Questa SIM 10.1b Support
 * Synplify Pro supported version G-2012.09-SP1
 * Support of LPDDR2 SDRAM Verilog designs
 * System Reset Pin Polarity selection
 * Additional clocks selection for AXI interface designs

(c) Copyright 2008 - 2021 Xilinx, Inc. All rights reserved.

This file contains confidential and proprietary information
of Xilinx, Inc. and is protected under U.S. and
international copyright and other intellectual property
laws.

DISCLAIMER
This disclaimer is not a license and does not grant any
rights to the materials distributed herewith. Except as
otherwise provided in a valid license issued to you by
Xilinx, and to the maximum extent permitted by applicable
law: (1) THESE MATERIALS ARE MADE AVAILABLE "AS IS" AND
WITH ALL FAULTS, AND XILINX HEREBY DISCLAIMS ALL WARRANTIES
AND CONDITIONS, EXPRESS, IMPLIED, OR STATUTORY, INCLUDING
BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, NON-
INFRINGEMENT, OR FITNESS FOR ANY PARTICULAR PURPOSE; and
(2) Xilinx shall not be liable (whether in contract or tort,
including negligence, or under any other theory of
liability) for any loss or damage of any kind or nature
related to, arising under or in connection with these
materials, including for any direct, or any indirect,
special, incidental, or consequential loss or damage
(including loss of data, profits, goodwill, or any type of
loss or damage suffered as a result of any action brought
by a third party) even if such damage or loss was
reasonably foreseeable or Xilinx had been advised of the
possibility of the same.

CRITICAL APPLICATIONS
Xilinx products are not designed or intended to be fail-
safe, or for use in any application requiring fail-safe
performance, such as life-support or safety devices or
systems, Class III medical devices, nuclear facilities,
applications related to the deployment of airbags, or any
other applications that could lead to death, personal
injury, or severe property or environmental damage
(individually and collectively, "Critical
Applications"). Customer assumes the sole risk and
liability of any use of Xilinx products in Critical
Applications, subject only to applicable laws and
regulations governing limitations on product liability.

THIS COPYRIGHT NOTICE AND DISCLAIMER MUST BE RETAINED AS
PART OF THIS FILE AT ALL TIMES.
