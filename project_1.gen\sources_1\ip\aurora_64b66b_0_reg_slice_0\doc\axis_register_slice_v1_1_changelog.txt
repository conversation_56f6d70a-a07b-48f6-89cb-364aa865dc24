2021.1:
 * Version 1.1 (Rev. 24)
 * Revision change in one or more subcores

2020.3:
 * Version 1.1 (Rev. 23)
 * Revision change in one or more subcores

2020.2.2:
 * Version 1.1 (Rev. 22)
 * No changes

2020.2.1:
 * Version 1.1 (Rev. 22)
 * No changes

2020.2:
 * Version 1.1 (Rev. 22)
 * General: Fix SLR TDM Crossing mode (type 13) to utilize SLL Regs (Laguna sites).
 * Revision change in one or more subcores

2020.1.1:
 * Version 1.1 (Rev. 21)
 * No changes

2020.1:
 * Version 1.1 (Rev. 21)
 * General: Fix behavioral sim of tied-off aresetn for auto-pipelining mode.
 * Revision change in one or more subcores

2019.2.2:
 * Version 1.1 (Rev. 20)
 * No changes

2019.2.1:
 * Version 1.1 (Rev. 20)
 * No changes

2019.2:
 * Version 1.1 (Rev. 20)
 * General: Added reserved modes for internal use.
 * Revision change in one or more subcores

2019.1.3:
 * Version 1.1 (Rev. 19)
 * No changes

2019.1.2:
 * Version 1.1 (Rev. 19)
 * No changes

2019.1.1:
 * Version 1.1 (Rev. 19)
 * No changes

2019.1:
 * Version 1.1 (Rev. 19)
 * General: axi_traffic_gen update from v2.0 to v3.0
 * Revision change in one or more subcores

2018.3.1:
 * Version 1.1 (Rev. 18)
 * No changes

2018.3:
 * Version 1.1 (Rev. 18)
 * General: Product Guide link updated.
 * Revision change in one or more subcores

2018.2:
 * Version 1.1 (Rev. 17)
 * New Feature: Added USER_SLL_REG attributes to automatically improve QOR of Multi-SLR-crossing mode.
 * Revision change in one or more subcores

2018.1:
 * Version 1.1 (Rev. 16)
 * New Feature: Added Multi-SLR-crossing mode with variable pipeline stages per SLR, with IP-level XDC for internal async reset pathway.
 * Other: clk_wiz update from v5.4 to v6.0

2017.4:
 * Version 1.1 (Rev. 15)
 * Revision change in one or more subcores

2017.3:
 * Version 1.1 (Rev. 14)
 * New Feature: Expose REG_CONFIG parameter in Config GUI to support user selection of register-slice type (default remains unchanged).
 * New Feature: Added SLR-crossing modes, including TDM at half-width at 2x CLK
 * New Feature: Added Bypass-Endpoint mode to allow interface properties to be treated as fixed values during IPI design validation.
 * Revision change in one or more subcores

2017.2:
 * Version 1.1 (Rev. 13)
 * Revision change in one or more subcores

2017.1:
 * Version 1.1 (Rev. 12)
 * Revision change in one or more subcores

2016.4:
 * Version 1.1 (Rev. 11)
 * Revision change in one or more subcores

2016.3:
 * Version 1.1 (Rev. 10)
 * Source HDL files are concatenated into a single file to speed up synthesis and simulation. No changes required by the user
 * Revision change in one or more subcores

2016.2:
 * Version 1.1 (Rev. 9)
 * Revision change in one or more subcores

2016.1:
 * Version 1.1 (Rev. 8)
 * Changes to HDL library management to support Vivado IP simulation library
 * Revision change in one or more subcores

2015.4.2:
 * Version 1.1 (Rev. 7)
 * No changes

2015.4.1:
 * Version 1.1 (Rev. 7)
 * No changes

2015.4:
 * Version 1.1 (Rev. 7)
 * Revision change in one or more subcores

2015.3:
 * Version 1.1 (Rev. 6)
 * IP revision number added to HDL module, library, and include file names, to support designs with both locked and upgraded IP instances

2015.2.1:
 * Version 1.1 (Rev. 5)
 * No changes

2015.2:
 * Version 1.1 (Rev. 5)
 * No changes

2015.1:
 * Version 1.1 (Rev. 5)
 * Enabled out-of-context clock frequency setting by adding FREQ_HZ parameter to clock interface CLKIF
 * The support status for Kintex UltraScale is changed from Pre-Production to Production.
 * Internal register slice improvements to reduce fanout on input ready signal.

2014.4.1:
 * Version 1.1 (Rev. 4)
 * No changes

2014.4:
 * Version 1.1 (Rev. 4)
 * Architecture support updated

2014.3:
 * Version 1.1 (Rev. 3)
 * Optimized to remove extra flops when there is no ready signal.

2014.2:
 * Version 1.1 (Rev. 2)
 * No changes

2014.1:
 * Version 1.1 (Rev. 2)
 * Internal device family name change, no functional changes

2013.4:
 * Version 1.1 (Rev. 1)
 * Kintex UltraScale Pre-Production support

2013.3:
 * Version 1.1
 * Added example design
 * Reduced warnings in synthesis and simulation

2013.2:
 * Version 1.0 (Rev. 1)
 * Architecture support updated

2013.1:
 * Version 1.0
 * Native Vivado Release
 * There have been no functional or interface changes to this IP.  The version number has changed to support unique versioning in Vivado starting with 2013.1.

(c) Copyright 2012 - 2021 Xilinx, Inc. All rights reserved.

This file contains confidential and proprietary information
of Xilinx, Inc. and is protected under U.S. and
international copyright and other intellectual property
laws.

DISCLAIMER
This disclaimer is not a license and does not grant any
rights to the materials distributed herewith. Except as
otherwise provided in a valid license issued to you by
Xilinx, and to the maximum extent permitted by applicable
law: (1) THESE MATERIALS ARE MADE AVAILABLE "AS IS" AND
WITH ALL FAULTS, AND XILINX HEREBY DISCLAIMS ALL WARRANTIES
AND CONDITIONS, EXPRESS, IMPLIED, OR STATUTORY, INCLUDING
BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, NON-
INFRINGEMENT, OR FITNESS FOR ANY PARTICULAR PURPOSE; and
(2) Xilinx shall not be liable (whether in contract or tort,
including negligence, or under any other theory of
liability) for any loss or damage of any kind or nature
related to, arising under or in connection with these
materials, including for any direct, or any indirect,
special, incidental, or consequential loss or damage
(including loss of data, profits, goodwill, or any type of
loss or damage suffered as a result of any action brought
by a third party) even if such damage or loss was
reasonably foreseeable or Xilinx had been advised of the
possibility of the same.

CRITICAL APPLICATIONS
Xilinx products are not designed or intended to be fail-
safe, or for use in any application requiring fail-safe
performance, such as life-support or safety devices or
systems, Class III medical devices, nuclear facilities,
applications related to the deployment of airbags, or any
other applications that could lead to death, personal
injury, or severe property or environmental damage
(individually and collectively, "Critical
Applications"). Customer assumes the sole risk and
liability of any use of Xilinx products in Critical
Applications, subject only to applicable laws and
regulations governing limitations on product liability.

THIS COPYRIGHT NOTICE AND DISCLAIMER MUST BE RETAINED AS
PART OF THIS FILE AT ALL TIMES.
