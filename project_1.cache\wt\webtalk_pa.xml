<?xml version="1.0" encoding="UTF-8" ?>
<document>
<!--The data in this file is primarily intended for consumption by Xilinx tools.
The structure and the elements are likely to change over the next few releases.
This means code written to parse this file will need to be revisited each subsequent release.-->
<application name="pa" timeStamp="Tue Jul 22 19:45:29 2025">
<section name="Project Information" visible="false">
<property name="ProjectID" value="aeb14d747bcb48b28791e4d5c40e010d" type="ProjectID"/>
<property name="ProjectIteration" value="1" type="ProjectIteration"/>
</section>
<section name="PlanAhead Usage" visible="true">
<item name="Project Data">
<property name="SrcSetCount" value="1" type="SrcSetCount"/>
<property name="ConstraintSetCount" value="1" type="ConstraintSetCount"/>
<property name="DesignMode" value="RTL" type="DesignMode"/>
<property name="SynthesisStrategy" value="Vivado Synthesis Defaults" type="SynthesisStrategy"/>
<property name="ImplStrategy" value="Vivado Implementation Defaults" type="ImplStrategy"/>
</item>
<item name="Java Command Handlers">
<property name="AddSources" value="49" type="JavaHandler"/>
<property name="CloseProject" value="1" type="JavaHandler"/>
<property name="CopyIPHandler" value="7" type="JavaHandler"/>
<property name="CoreView" value="12" type="JavaHandler"/>
<property name="CustomizeCore" value="29" type="JavaHandler"/>
<property name="EditDelete" value="20" type="JavaHandler"/>
<property name="ExitApp" value="10" type="JavaHandler"/>
<property name="OpenIPExampleDesign" value="2" type="JavaHandler"/>
<property name="OpenProject" value="5" type="JavaHandler"/>
<property name="RecustomizeCore" value="146" type="JavaHandler"/>
<property name="ReportIPStatus" value="2" type="JavaHandler"/>
<property name="SetTopNode" value="9" type="JavaHandler"/>
<property name="ShowView" value="3" type="JavaHandler"/>
<property name="SimulationClose" value="1" type="JavaHandler"/>
<property name="SimulationRun" value="74" type="JavaHandler"/>
<property name="ToolsSettings" value="16" type="JavaHandler"/>
<property name="UpgradeIP" value="2" type="JavaHandler"/>
</item>
<item name="Gui Handlers">
<property name="AbstractCombinedPanel_REMOVE_SELECTED_ELEMENTS" value="1" type="GuiHandlerData"/>
<property name="AbstractFileView_RELOAD" value="2" type="GuiHandlerData"/>
<property name="AddSrcWizard_SPECIFY_SIMULATION_SPECIFIC_HDL_FILES" value="4" type="GuiHandlerData"/>
<property name="BaseDialog_APPLY" value="5" type="GuiHandlerData"/>
<property name="BaseDialog_CANCEL" value="38" type="GuiHandlerData"/>
<property name="BaseDialog_OK" value="108" type="GuiHandlerData"/>
<property name="CmdMsgDialog_OPEN_MESSAGES_VIEW" value="1" type="GuiHandlerData"/>
<property name="CommandsInput_TYPE_TCL_COMMAND_HERE" value="1" type="GuiHandlerData"/>
<property name="CopyIPHandler_DESTINATION_IP_LOCATION" value="1" type="GuiHandlerData"/>
<property name="CopyIPHandler_DESTINATION_IP_NAME" value="7" type="GuiHandlerData"/>
<property name="CoreTreeTablePanel_CORE_TREE_TABLE" value="85" type="GuiHandlerData"/>
<property name="CreateSrcFileDialog_FILE_NAME" value="16" type="GuiHandlerData"/>
<property name="FileSetPanel_FILE_SET_PANEL_TREE" value="2819" type="GuiHandlerData"/>
<property name="FlowNavigatorTreePanel_FLOW_NAVIGATOR_TREE" value="91" type="GuiHandlerData"/>
<property name="GettingStartedView_OPEN_PROJECT" value="3" type="GuiHandlerData"/>
<property name="HACGCIPSymbol_SHOW_DISABLED_PORTS" value="16" type="GuiHandlerData"/>
<property name="HCodeEditor_CLOSE" value="12" type="GuiHandlerData"/>
<property name="HCodeEditor_SEARCH_TEXT_COMBO_BOX" value="1" type="GuiHandlerData"/>
<property name="IPStatusSectionPanel_UPGRADE_SELECTED" value="2" type="GuiHandlerData"/>
<property name="IPStatusTablePanel_IP_STATUS_TABLE" value="1" type="GuiHandlerData"/>
<property name="MainMenuMgr_EDIT" value="2" type="GuiHandlerData"/>
<property name="MainMenuMgr_FILE" value="10" type="GuiHandlerData"/>
<property name="MainMenuMgr_FLOW" value="4" type="GuiHandlerData"/>
<property name="MainMenuMgr_HELP" value="2" type="GuiHandlerData"/>
<property name="MainMenuMgr_PROJECT" value="4" type="GuiHandlerData"/>
<property name="MainMenuMgr_REPORTS" value="10" type="GuiHandlerData"/>
<property name="MainMenuMgr_TOOLS" value="28" type="GuiHandlerData"/>
<property name="MainMenuMgr_WINDOW" value="4" type="GuiHandlerData"/>
<property name="MsgTreePanel_MESSAGE_VIEW_TREE" value="5" type="GuiHandlerData"/>
<property name="OpenIPExampleDesign_EXAMPLE_PROJECT_DIRECTORY" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_ADD_SOURCES" value="50" type="GuiHandlerData"/>
<property name="PACommandNames_AUTO_UPDATE_HIER" value="18" type="GuiHandlerData"/>
<property name="PACommandNames_COPY_IP" value="7" type="GuiHandlerData"/>
<property name="PACommandNames_OPEN_IP_EXAMPLE_DESIGN" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_OPEN_PROJECT" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_RECUSTOMIZE_CORE" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_REPORT_IP_STATUS" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_SET_AS_TOP" value="9" type="GuiHandlerData"/>
<property name="PACommandNames_SIMULATION_CLOSE" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_SIMULATION_RESET_BEHAVIORAL" value="3" type="GuiHandlerData"/>
<property name="PACommandNames_SIMULATION_RUN_BEHAVIORAL" value="72" type="GuiHandlerData"/>
<property name="PACommandNames_SIMULATION_RUN_POST_SYNTHESIS_FUNCTIONAL" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_SIMULATION_SETTINGS" value="1" type="GuiHandlerData"/>
<property name="PAViews_CODE" value="50" type="GuiHandlerData"/>
<property name="PAViews_IP_CATALOG" value="3" type="GuiHandlerData"/>
<property name="PAViews_PROJECT_SUMMARY" value="1" type="GuiHandlerData"/>
<property name="ProgressDialog_BACKGROUND" value="12" type="GuiHandlerData"/>
<property name="ProgressDialog_CANCEL" value="3" type="GuiHandlerData"/>
<property name="ProjectSettingsSimulationPanel_TABBED_PANE" value="10" type="GuiHandlerData"/>
<property name="ProjectSettingsSimulationPanel_TARGET_SIMULATOR" value="8" type="GuiHandlerData"/>
<property name="RDICommands_COPY" value="1" type="GuiHandlerData"/>
<property name="RDICommands_CUSTOM_COMMANDS" value="12" type="GuiHandlerData"/>
<property name="RDICommands_DELETE" value="20" type="GuiHandlerData"/>
<property name="RDICommands_SETTINGS" value="15" type="GuiHandlerData"/>
<property name="RemoveSourcesDialog_ALSO_DELETE" value="6" type="GuiHandlerData"/>
<property name="ReportIPStatusInfoDialog_IGNORE" value="1" type="GuiHandlerData"/>
<property name="RunGadget_SHOW_WARNING_AND_ERROR_MESSAGES_IN_MESSAGES" value="2" type="GuiHandlerData"/>
<property name="SettingsDialog_OPTIONS_TREE" value="11" type="GuiHandlerData"/>
<property name="SettingsDialog_PROJECT_TREE" value="14" type="GuiHandlerData"/>
<property name="SettingsEditorPage_ENTER_COMMAND_LINE_FOR_CUSTOM" value="1" type="GuiHandlerData"/>
<property name="SettingsEditorPage_USE_THIS_DROP_DOWN_LIST_BOX_TO_SELECT" value="1" type="GuiHandlerData"/>
<property name="SettingsProjectGeneralPage_CHOOSE_DEVICE_FOR_YOUR_PROJECT" value="1" type="GuiHandlerData"/>
<property name="SettingsThirdPartyToolsPage_SPECIFY_MODELSIM_COMPILED_LIBRARY_PATH" value="2" type="GuiHandlerData"/>
<property name="SettingsThirdPartyToolsPage_SPECIFY_MODELSIM_INSTALL_PATH" value="1" type="GuiHandlerData"/>
<property name="SimpleOutputProductDialog_GENERATE_OUTPUT_PRODUCTS_IMMEDIATELY" value="35" type="GuiHandlerData"/>
<property name="SrcChooserPanel_ADD_DIRECTORIES" value="1" type="GuiHandlerData"/>
<property name="SrcChooserPanel_ADD_HDL_AND_NETLIST_FILES_TO_YOUR_PROJECT" value="13" type="GuiHandlerData"/>
<property name="SrcChooserPanel_ADD_OR_CREATE_SOURCE_FILE" value="3" type="GuiHandlerData"/>
<property name="SrcChooserPanel_CHANGE_SOURCE_PROPERTIES" value="1" type="GuiHandlerData"/>
<property name="SrcChooserPanel_CREATE_FILE" value="17" type="GuiHandlerData"/>
<property name="SrcChooserPanel_MAKE_LOCAL_COPY_OF_THESE_FILES_INTO" value="3" type="GuiHandlerData"/>
<property name="SrcChooserTable_SRC_CHOOSER_TABLE" value="8" type="GuiHandlerData"/>
<property name="SrcMenu_IP_DOCUMENTATION" value="20" type="GuiHandlerData"/>
<property name="SrcMenu_IP_HIERARCHY" value="12" type="GuiHandlerData"/>
<property name="SyntheticaGettingStartedView_RECENT_PROJECTS" value="5" type="GuiHandlerData"/>
<property name="TclConsoleView_TCL_CONSOLE_CODE_EDITOR" value="20" type="GuiHandlerData"/>
</item>
<item name="Other">
<property name="GuiMode" value="47" type="GuiMode"/>
<property name="BatchMode" value="0" type="BatchMode"/>
<property name="TclMode" value="19" type="TclMode"/>
</item>
</section>
</application>
</document>
