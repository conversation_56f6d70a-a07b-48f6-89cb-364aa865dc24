2021.1:
 * Version 4.1 (Rev. 12)
 * General: Added xcux35,vu19p_CIV,vu31p_CIV and vu45p_CIVdevice support.
 * Revision change in one or more subcores

2020.3:
 * Version 4.1 (Rev. 11)
 * General: Added xqzu4xdr device support.
 * Revision change in one or more subcores

2020.2.2:
 * Version 4.1 (Rev. 10)
 * General: Added xcvu080_CIV device support.
 * Revision change in one or more subcores

2020.2.1:
 * Version 4.1 (Rev. 9)
 * General: Fixed vu19p gt_channel selection issue.
 * General: Added xcvu47p_CIV device support.
 * Revision change in one or more subcores

2020.2:
 * Version 4.1 (Rev. 8)
 * General: Added XCZU43DR and XCVU57P device support.
 * Revision change in one or more subcores

2020.1.1:
 * Version 4.1 (Rev. 7)
 * General: Added XQVU9P and XQVU13P device support.
 * Revision change in one or more subcores

2020.1:
 * Version 4.1 (Rev. 6)
 * General: Added a GUI option, in "Debug" tab, to enable store_ltssm logic
 * General: Shared logic not supported in the example design when Tandem is enabled.
 * Revision change in one or more subcores

2019.2.2:
 * Version 4.1 (Rev. 5)
 * General: Added device support for xcvu15p and xczu5xdr devices.
 * Revision change in one or more subcores

2019.2.1:
 * Version 4.1 (Rev. 4)
 * No changes

2019.2:
 * Version 4.1 (Rev. 4)
 * General: Added Tandem support for xcvu45p and xcvu47p devices.
 * Revision change in one or more subcores

2019.1.3:
 * Version 4.1 (Rev. 3)
 * No changes

2019.1.2:
 * Version 4.1 (Rev. 3)
 * No changes

2019.1.1:
 * Version 4.1 (Rev. 3)
 * No changes

2019.1:
 * Version 4.1 (Rev. 3)
 * Bug Fix: Fixed core generation issue for -1M device with Gen2 and 125 MHz AXI clock frequencey.
 * Bug Fix: Fixed back to back reads failure for 7series Gen2 DMA.
 * Feature Enhancement: Added GUI option to disable the GT channel LOC constraints in GTWizard IP XDC
 * Revision change in one or more subcores

2018.3.1:
 * Version 4.1 (Rev. 2)
 * No changes

2018.3:
 * Version 4.1 (Rev. 2)
 * Bug Fix: Disabled vendor ID change for non PF0 case
 * Bug Fix: Fixed MSI-X packet corruption in case Gen2 devices.
 * Bug Fix: Added an option in the GUI to enable lane reversal parameter for Gen2 devices
 * Bug Fix: Fixed Slot Capabilities register offset for Root Port example design in Bridge mode
 * Bug Fix: Fixed Bridge address translation issue for 32bit AXI BAR to 64bit PCIe BAR(when AXI Address width is lessthan 64)
 * Bug Fix: Fixed IPI block automation for vu440 device
 * Feature Enhancement: Added Resizable BAR for SDAccel
 * Feature Enhancement: Added EROM functionality
 * Feature Enhancement: Added Parity option for AXI Bridge mode. Added s_axib_wuser and s_axib_ruser signal when it's enabled
 * Feature Enhancement: Added an option in the GUI to select PCIe IDs through ports
 * Feature Enhancement: Added an option in the GUI to select FIFO mode or decode mode for MSI interrupt in Root Port Bridge mode
 * Feature Enhancement: Added an option in the GUI to select MPSOC Root Port solution in basic page for Bridge Root Port mode
 * Feature Enhancement: Added an option in the GUI to enable optional debug ports cfg_current_speed and cfg_negotiated_width
 * Revision change in one or more subcores

2018.2:
 * Version 4.1 (Rev. 1)
 * Bug Fix: Allowed AXI-MM Bypass BAR size till 256GB for 64bit BAR (Xilinx Answer Record - 71012)
 * Bug Fix: Removed dependency of 'GT Channel DRP' parameter on 'Enable In System IBERT' parameter. Now, both the options can be selected at the same time
 * Bug Fix: Slave AXI Lite memory range is updated to 64KB for DMA functional mode and 512MB for AXI Bridge functional mode
 * Bug Fix: Fixed Posted TLP ordering issue on Slave AXI Lite and Slave AXI interfaces
 * Bug Fix: Fixed Master MemWr and received interrupt ordering issue for AxiBridge RC configuration
 * Bug Fix: Fix for CQ NP credits after Partial Reconfiguration
 * Bug Fix: Fixed 7series Gen2 DMA hang issue due to TLP drop and incorrect TLP for straddled packets
 * Feature Enhancement: Enabled x16g3 configuration support for -1 speedgrade of  UltraScale Plus family devices
 * Other: Performance improved for axi bridge slave interface (Xilinx Answer Record - 71052)
 * Other: Added support for the defense grade devices xqvu7p,xqvu11p,xqzu5ev,xqzu7ev,xqzu19eg,xqzu21dr and xqzu28dr
 * Other: Added support for xcvu29p device
 * Revision change in one or more subcores

2018.1:
 * Version 4.1
 * Bug Fix: Fixed the issue with the H2C transfer where completions are not returned when odd number of bytes are being transferred (Xilinx Answer Record - 70324)
 * Bug Fix: Fixed the issue with the C2H transfer where s_axis_cc_tready signal is not asserted when odd number of bytes are being transferred (Xilinx Answer Record - 70324)
 * Bug Fix: Fixed issue with H2C transfer when 128bit axi datawidth and 64 bit address is enabled (Xilinx Answer Record - 70324)
 * Bug Fix: Force overwrite PCIEBAR2AXIBAR translation vector to 0x0 is now allowed when PCIe BAR is disabled to turn off Address Translation feature in Root Port AXI Bridge mode if chosen
 * Bug Fix: Fixed the issue where axi_ctl_aresetn is de-asserted before axi_clk is stable for UltraScale Plus devices
 * Bug Fix: Changed the interface mode of dsc_bypass_c2h_* and dsc_bypass_h2c_* interfaces to slave
 * Feature Enhancement: Added GEN4 control skip filter module with parameter(ctrl_skip_mask).This can be used for EndPoint to work with GEN4 RootPort generating control skips
 * Feature Enhancement: Added ASPM L1 support for GEN3 and ASPM L0s for Gen1 & Gen2 with GUI option(in PCIe:MISC page) for Bridge Endpoint mode
 * Feature Enhancement: Added a new optional dma_bridge_resetn pin that allows DMA/Bridge engine/registers to be reset without bringing PCIe link down. This can be enabled through soft_reset_en parameter
 * Feature Enhancement: Added a new Interrupt Decode mode for RootPort Bridge can be enabled via TCL parameter CONFIG.msi_rx_pin_en(true). Enabling this option will allow the IP to internally decode Legacy and MSI interrupt and remove the use of Interrupt FIFO. Bridge Info Register [5] is updated to reflect this change
 * Feature Enhancement: Added msix_enable output port when msix is enabled
 * Feature Enhancement: Added pcie_id input ports for UltraScalePlus devices with parameter(pcie_id_if)
 * Feature Enhancement: Rename Parity as Data Protection in GUI
 * Feature Enhancement: Added Tandem XDMA support for the xczu7ev, xczu7eg, xczu7cg, xcku11p, and xcvu11p devices.
 * Feature Enhancement: Added support for fsve1156,fsvg1517 packages for xczu25dr,xczu27dr,xczu28dr devices and fsvf1760 for xczu29dr device
 * Other: Bridge mode Root Port only: By default, axi_ctl_aresetn now deasserts after PHY is ready (GT Initialization done)
 * Other: Bridge mode Endpoint only: By default, axi_ctl_aresetn now deasserts after PCIe link is in D0_active power state (PCIe link up, configured, and enabled)
 * Revision change in one or more subcores

2017.4:
 * Version 4.0 (Rev. 1)
 * Bug Fix: Allow MSI-X Table and PBA registers to be programed while MSI-X Enable bit in MSIX Control register is 0. (Affects Bridge functional mode only)
 * Bug Fix: Corrected MSI-X Table Size to 'h1F (32 vectors)
 * Bug Fix: Corrected CC to TX conversion which was causing register read failures when there is high C2H traffic. (this issue affects both DMA and Bridge functional mode - 7 Series only)
 * Bug Fix: Fixed the receive data for PCIe Hard Block when 64 bit addressing is enabled. (this issue affects both DMA and Bridge functional mode - 7 Series only)
 * Bug Fix: Fixed ext_sys_clk_bufg option in US+
 * Feature Enhancement: Added Auto Rx Equalization support in the GT Settings GUI page.
 * Revision change in one or more subcores

2017.3:
 * Version 4.0
 * Bug Fix: Fixed issue with the generation of example design in IPI canvas
 * Bug Fix: Updated arrow colors for JTAG debugger LTSSM graph
 * Bug Fix: Added missing ports for 'Include GT Wizard in Example Design' mode
 * Bug Fix: Corrected GT DRP address width for Ultrascale Plus device family
 * Bug Fix: Fixed connection between cfg_err_cor_in or cfg_err_uncor_in signals and XDMA Bridge internal register. Remove unused cfg_err_uncor_in port in multi PF designs
 * Bug Fix: Removed invalid dependency on M_AXIB_Wvalid and M_AXIB_AWready signal
 * Bug Fix: Fixed Address Translation mechanism in RP AXI Bridge mode when no BAR is enabled
 * Bug Fix: Fixed Address Translation for 64-bit AXI/PCIe BAR
 * Feature Enhancement: Added option to enable external BUFG_GT/SYNC for sys_clk
 * Feature Enhancement: Moved phy_clk module in support wrapper when Include GT Wizard in Example Design mode is selected
 * Feature Enhancement: Added GT COMMON sharing support
 * Feature Enhancement: Added Tandem support for VU7P, KU15P, ZU19 devices
 * Feature Enhancement: Added support for xcvu33p, xazu4ev and xazu5ev devices
 * Feature Enhancement: Enabled x8g3 configuration support for -1 and -2LV speedgrades for all the devices and packages
 * Other: Removed s_axib_wuser and s_axib_ruser signal in AXI Bridge mode
 * Revision change in one or more subcores

2017.2:
 * Version 3.1 (Rev. 1)
 * Bug Fix: PCI Express Extended Configuration Space Enable parameter enables config extended interface
 * Bug Fix: Limit PCIe BAR and address translation value for AXI Lite Master interface to 32-bit only
 * Bug Fix: Limit PCIe BAR and address translation value for DMA Bridge mode up to the chosen AXI Address Width only
 * Bug Fix: Disable Slave AXI Bridge Timeout Counter and use PCIe Hard Block Timeout Counter for Vrtex7-XT, UltraScale and UltraScale Plus devices
 * Bug Fix: Handle Message TLP for 7-series Gen2 DMA
 * Feature Enhancement: Added support for xczu27dr device
 * Revision change in one or more subcores

2017.1:
 * Version 3.1
 * Bug Fix: Fixed axi_aresetn output signal driving based on user_reset
 * Bug Fix: Added simulation support for 128byte DMA
 * Bug Fix: Enabled support for g1x16, g2x16 configurations for -1L, -1LV and -2LV speedgrades for UltraScale Plus device family
 * Bug Fix: Removed option to select 250Mhz of UserClk_freq (AXI Interface Frequency) for the devices xc7z015,xc7z15i and xc7z12s since the timing does not meet
 * Bug Fix: Removed m_axib_ruser and m_axib_wuser from M_AXI_B interface when AXI Bridge mode is selected, since these signals are not used in this mode. No change in M_AXI_BYPASS interface when DMA mode is selected
 * Feature Enhancement: Added support for xczu7ev device
 * Feature Enhancement: Added support for xczu21dr, xczu25dr, xczu28dr and xczu29dr devices
 * Feature Enhancement: Added JTAG debugger support in the Add. Debug Options GUI page to debug LTSSM, Reset sequence and Rx detect sequence.
 * Feature Enhancement: Added In-System IBERT support in the Add. Debug Options page to scan eye diagram of the serial lane
 * Feature Enhancement: Added support for zynquplus devices - xczu4cg,xczu4eg, xczu5cg, xczu5eg, xczu7cg and xczu7eg
 * Feature Enhancement: Enabled g3x16 support for all the UltraScale Plus devices and packages for -2L speedgrades
 * Feature Enhancement: Removed g3x16 and g3x8 option for -1 speedgrade for all the UltraScale Plus devices and packages
 * Feature Enhancement: For all the UltraScale Plus devices and pacakges, Removed the option to select the coreclock frequency. It is always set to 500MHz now.
 * Feature Enhancement: Added support for external GT DRP interface
 * Feature Enhancement: Added option to enable PM_L23_Entry
 * Feature Enhancement: Added GT Wizard support for Xilinx example design in the Shared Logic page.
 * Feature Enhancement: Added Tandem support for vu3p, vu9p, and vu13p.
 * Feature Enhancement: Change the default value of User Interrupt Enable Mask in IRQ Block register to always allow interrupt after reset when Bridge functional mode is selected.
 * Revision change in one or more subcores

2016.4:
 * Version 3.0 (Rev. 1)
 * Bug Fix: Fixed simulation issue with Vivado simulator
 * Feature Enhancement: Added AXI Bridge functionality for UltraScale Plus family devices
 * Feature Enhancement: Enabled x16g3 support for -2L speedgrades for xcvu9p - flga2104,flgb2104,flgc2104 and fsgd2104
 * Revision change in one or more subcores

2016.3:
 * Version 3.0
 * Port Change: Added debug ports. When option 'Enable Debug Ports' is selected on 'PCIe DMA' tab all m_axis_rq/cc_* and s_axis_rc/cq_* signals will appear at boundary
 * Bug Fix: Fixed issues with the example design generation for xcvu9p-flgc2104 and flga2577 packages
 * Feature Enhancement: Added support for PCIe Gen2 devcies
 * Feature Enhancement: Added shared logic support for 7 series and UltraScale variants. Currently Shared logic is not supported when Tandem feature is selected.
 * Feature Enhancement: Added Check parity to check parity on pcie reads and add parity for pcie writes. Added Parity propagate to send and receive parity bits to/from user
 * Revision change in one or more subcores

2016.2:
 * Version 2.0 (Rev. 1)
 * Fixed issue with the generation of acknowledgement for interrupt assertion and de-assertion when multiple interrupts are generated.
 * Removed the GUI selections for AXI Data Widths that are not supported for -1 speedgrade parts.
 * Updated the Tandem with Field Updates example design scripts to handle IP core containers where the output products were not generated.
 * Added support for defense grade Kintex UltraScale device xqku115
 * Modified the insertion loss profile parameter to provide three options Chip-to-Chip(5db), Add-in_Card(15db) and Backplane(20db). Core operates in LPM mode for the values < 15db and DFE mode for the values >= 15db.
 * Revision change in one or more subcores

2016.1:
 * Version 2.0
 * Modified the width of pipe_tx_*_sigs, common_commands_in and common_commands_out
 * Modified the mapping of logical and physical external pipe interface ports for End Point configurations so that it can be connected to Root Port device directly
 * Changes to HDL library management to support Vivado IP simulation library
 * Added Tandem and MCAP support for Ultrascale configuration of the core.
 * Removed m_axis_h2c_tkeep_0/1/2/3 signals
 * Added option to disable pcie_cfg_mgmt interface depending on the parameter Configuration Management Interface
 * Added support for defense grade kintexu devices - xqku040-rfa1156,xqku040-rba676,xqku060-rfa1156 and xqku095-rfa1156
 * Added bus interfaces for dsc_bypass_c2h_* and dsc_bypass_h2c_* signals for all the 4 channels
 * Added interface dma_status_ports for c2h_sts_* and h2c_sts_* signals
 * Fixed issue with the default values of 'Base_Class_Menu' and 'Sub Class_Interface_Menu' 
 * Added the required Tandem and MCAP ports: cap_*, startup_*, mcap_* interfaces
 * Added HAS_BURST parameter on M_AXI interfaces for SmartConnect optimization
 * Added two new ports to transceiver debug interface section: gt_dmonfiforeset gt_dmonitorclk.
 * Revision change in one or more subcores

2015.4.2:
 * Version 1.0 (Rev. 1)
 * No changes

2015.4.1:
 * Version 1.0 (Rev. 1)
 * No changes

2015.4:
 * Version 1.0 (Rev. 1)
 * Added support for ffva1156 package for xcku095 device
 * Revision change in one or more subcores

2015.3:
 * Version 1.0
 * Initial release

(c) Copyright 2015 - 2021 Xilinx, Inc. All rights reserved.

This file contains confidential and proprietary information
of Xilinx, Inc. and is protected under U.S. and
international copyright and other intellectual property
laws.

DISCLAIMER
This disclaimer is not a license and does not grant any
rights to the materials distributed herewith. Except as
otherwise provided in a valid license issued to you by
Xilinx, and to the maximum extent permitted by applicable
law: (1) THESE MATERIALS ARE MADE AVAILABLE "AS IS" AND
WITH ALL FAULTS, AND XILINX HEREBY DISCLAIMS ALL WARRANTIES
AND CONDITIONS, EXPRESS, IMPLIED, OR STATUTORY, INCLUDING
BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, NON-
INFRINGEMENT, OR FITNESS FOR ANY PARTICULAR PURPOSE; and
(2) Xilinx shall not be liable (whether in contract or tort,
including negligence, or under any other theory of
liability) for any loss or damage of any kind or nature
related to, arising under or in connection with these
materials, including for any direct, or any indirect,
special, incidental, or consequential loss or damage
(including loss of data, profits, goodwill, or any type of
loss or damage suffered as a result of any action brought
by a third party) even if such damage or loss was
reasonably foreseeable or Xilinx had been advised of the
possibility of the same.

CRITICAL APPLICATIONS
Xilinx products are not designed or intended to be fail-
safe, or for use in any application requiring fail-safe
performance, such as life-support or safety devices or
systems, Class III medical devices, nuclear facilities,
applications related to the deployment of airbags, or any
other applications that could lead to death, personal
injury, or severe property or environmental damage
(individually and collectively, "Critical
Applications"). Customer assumes the sole risk and
liability of any use of Xilinx products in Critical
Applications, subject only to applicable laws and
regulations governing limitations on product liability.

THIS COPYRIGHT NOTICE AND DISCLAIMER MUST BE RETAINED AS
PART OF THIS FILE AT ALL TIMES.
