<?xml version="1.0" encoding="UTF-8"?>
<spirit:component xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>xilinx.com</spirit:vendor>
  <spirit:library>customized_ip</spirit:library>
  <spirit:name>xdma_v4_1_12_blk_mem_64_reg_be</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:busInterfaces>
    <spirit:busInterface>
      <spirit:name>CLK.ACLK</spirit:name>
      <spirit:displayName>ACLK</spirit:displayName>
      <spirit:description>AXI4 Interconnect Clock Input</spirit:description>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_aclk</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_BUSIF</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.CLK.ACLK.ASSOCIATED_BUSIF">AXI_SLAVE_S_AXI:AXILite_SLAVE_S_AXI</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_RESET</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.CLK.ACLK.ASSOCIATED_RESET">s_aresetn</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLK.ACLK.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_TOLERANCE_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLK.ACLK.FREQ_TOLERANCE_HZ">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLK.ACLK.PHASE">0.0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLK.ACLK.CLK_DOMAIN"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.CLK.ACLK.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>RST.ARESETN</spirit:name>
      <spirit:displayName>ARESETN</spirit:displayName>
      <spirit:description>AXI4 Interconnect Reset Input</spirit:description>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_aresetn</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.RST.ARESETN.POLARITY">ACTIVE_LOW</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.RST.ARESETN.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>AXI_SLAVE_S_AXI</spirit:name>
      <spirit:displayName>AXI_SLAVE</spirit:displayName>
      <spirit:description>AXI_SLAVE</spirit:description>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="aximm" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="aximm_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARADDR</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_araddr</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARBURST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_arburst</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_arid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARLEN</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_arlen</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_arready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARSIZE</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_arsize</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_arvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWADDR</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awaddr</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWBURST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awburst</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWLEN</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awlen</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWSIZE</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awsize</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_bid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_bready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BRESP</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_bresp</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_bvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RDATA</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rdata</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RLAST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rlast</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RRESP</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rresp</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WDATA</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_wdata</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WLAST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_wlast</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_wready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WSTRB</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_wstrb</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_wvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>DATA_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.DATA_WIDTH">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PROTOCOL</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.PROTOCOL">AXI4LITE</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ID_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.ID_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ADDR_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.ADDR_WIDTH">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>AWUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.AWUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ARUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.ARUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>WUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.WUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>RUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.RUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>BUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.BUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>READ_WRITE_MODE</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.READ_WRITE_MODE">READ_WRITE</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_BURST</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_BURST">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_LOCK</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_LOCK">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_PROT</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_PROT">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_CACHE</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_CACHE">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_QOS</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_QOS">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_REGION</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_REGION">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_WSTRB</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_WSTRB">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_BRESP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_BRESP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_RRESP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_RRESP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>SUPPORTS_NARROW_BURST</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.SUPPORTS_NARROW_BURST">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>NUM_READ_OUTSTANDING</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.NUM_READ_OUTSTANDING">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>NUM_WRITE_OUTSTANDING</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.NUM_WRITE_OUTSTANDING">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>MAX_BURST_LENGTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.MAX_BURST_LENGTH">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.PHASE">0.0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.CLK_DOMAIN"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>NUM_READ_THREADS</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.NUM_READ_THREADS">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>NUM_WRITE_THREADS</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.NUM_WRITE_THREADS">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>RUSER_BITS_PER_BYTE</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.RUSER_BITS_PER_BYTE">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>WUSER_BITS_PER_BYTE</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.WUSER_BITS_PER_BYTE">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.AXI_SLAVE_S_AXI" xilinx:dependency="(((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)))=1) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_TYPE&apos;)))= 1))">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>AXILite_SLAVE_S_AXI</spirit:name>
      <spirit:displayName>AXILite_SLAVE</spirit:displayName>
      <spirit:description>AXILite_SLAVE</spirit:description>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="aximm" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="aximm_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARADDR</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_araddr</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARBURST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_arburst</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_arid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARLEN</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_arlen</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_arready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARSIZE</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_arsize</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_arvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWADDR</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awaddr</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWBURST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awburst</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWLEN</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awlen</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWSIZE</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awsize</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_awvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_bid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_bready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BRESP</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_bresp</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_bvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RDATA</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rdata</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RLAST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rlast</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RRESP</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rresp</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_rvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WDATA</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_wdata</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WLAST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_wlast</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_wready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WSTRB</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_wstrb</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axi_wvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>DATA_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.DATA_WIDTH">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PROTOCOL</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.PROTOCOL">AXI4LITE</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ID_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.ID_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ADDR_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.ADDR_WIDTH">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>AWUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.AWUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ARUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.ARUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>WUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.WUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>RUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.RUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>BUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.BUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>READ_WRITE_MODE</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.READ_WRITE_MODE">READ_WRITE</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_BURST</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_BURST">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_LOCK</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_LOCK">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_PROT</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_PROT">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_CACHE</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_CACHE">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_QOS</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_QOS">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_REGION</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_REGION">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_WSTRB</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_WSTRB">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_BRESP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_BRESP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_RRESP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_RRESP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>SUPPORTS_NARROW_BURST</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.SUPPORTS_NARROW_BURST">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>NUM_READ_OUTSTANDING</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.NUM_READ_OUTSTANDING">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>NUM_WRITE_OUTSTANDING</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.NUM_WRITE_OUTSTANDING">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>MAX_BURST_LENGTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.MAX_BURST_LENGTH">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.PHASE">0.0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.CLK_DOMAIN"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>NUM_READ_THREADS</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.NUM_READ_THREADS">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>NUM_WRITE_THREADS</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.NUM_WRITE_THREADS">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>RUSER_BITS_PER_BYTE</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.RUSER_BITS_PER_BYTE">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>WUSER_BITS_PER_BYTE</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.WUSER_BITS_PER_BYTE">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.AXILite_SLAVE_S_AXI" xilinx:dependency="(((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)))=1) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_TYPE&apos;)))= 0))">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>BRAM_PORTA</spirit:name>
      <spirit:displayName>BRAM_PORTA</spirit:displayName>
      <spirit:description>BRAM_PORTA</spirit:description>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="bram" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="bram_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ADDR</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>addra</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>clka</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>DIN</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>dina</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>DOUT</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>douta</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>EN</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>ena</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>rsta</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WE</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>wea</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>MEM_SIZE</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.BRAM_PORTA.MEM_SIZE">8192</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>MEM_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.BRAM_PORTA.MEM_WIDTH">32</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>MEM_ECC</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.BRAM_PORTA.MEM_ECC">NONE</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>MASTER_TYPE</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.BRAM_PORTA.MASTER_TYPE">OTHER</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>READ_WRITE_MODE</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.BRAM_PORTA.READ_WRITE_MODE"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>READ_LATENCY</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.BRAM_PORTA.READ_LATENCY">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.BRAM_PORTA" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;))=0">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>BRAM_PORTB</spirit:name>
      <spirit:displayName>BRAM_PORTB</spirit:displayName>
      <spirit:description>BRAM_PORTB</spirit:description>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="bram" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="bram_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ADDR</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>addrb</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>clkb</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>DIN</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>dinb</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>DOUT</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>doutb</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>EN</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>enb</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>rstb</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WE</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>web</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>MEM_SIZE</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.BRAM_PORTB.MEM_SIZE">8192</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>MEM_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.BRAM_PORTB.MEM_WIDTH">32</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>MEM_ECC</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.BRAM_PORTB.MEM_ECC">NONE</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>MASTER_TYPE</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.BRAM_PORTB.MASTER_TYPE">OTHER</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>READ_WRITE_MODE</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.BRAM_PORTB.READ_WRITE_MODE"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>READ_LATENCY</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.BRAM_PORTB.READ_LATENCY">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.BRAM_PORTB" xilinx:dependency="(((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)))=0) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)))!= 0) and ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)))!= 3))">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
  </spirit:busInterfaces>
  <spirit:memoryMaps>
    <spirit:memoryMap>
      <spirit:name>S_1</spirit:name>
      <spirit:addressBlock>
        <spirit:name>Mem0</spirit:name>
        <spirit:baseAddress spirit:format="long">0</spirit:baseAddress>
        <spirit:range spirit:format="long" spirit:resolve="generated">4096</spirit:range>
        <spirit:width spirit:format="long">32</spirit:width>
        <spirit:usage>memory</spirit:usage>
        <spirit:access>read-write</spirit:access>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>OFFSET_BASE_PARAM</spirit:name>
            <spirit:value spirit:id="ADDRBLOCKPARAM_VALUE.S_1.MEM0.OFFSET_BASE_PARAM">C_BASEADDR</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>OFFSET_HIGH_PARAM</spirit:name>
            <spirit:value spirit:id="ADDRBLOCKPARAM_VALUE.S_1.MEM0.OFFSET_HIGH_PARAM">C_HIGHADDR</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:addressBlock>
    </spirit:memoryMap>
  </spirit:memoryMaps>
  <spirit:model>
    <spirit:views>
      <spirit:view>
        <spirit:name>xilinx_vhdlsynthesis</spirit:name>
        <spirit:displayName>VHDL Synthesis</spirit:displayName>
        <spirit:envIdentifier>vhdlSource:vivado.xilinx.com:synthesis</spirit:envIdentifier>
        <spirit:language>vhdl</spirit:language>
        <spirit:modelName>blk_mem_gen_v8_4_4</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_vhdlsynthesis_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Fri Jul 11 02:16:37 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:59dbb152</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_synthesisconstraints</spirit:name>
        <spirit:displayName>Synthesis Constraints</spirit:displayName>
        <spirit:envIdentifier>:vivado.xilinx.com:synthesis.constraints</spirit:envIdentifier>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:59dbb152</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_vhdlsynthesiswrapper</spirit:name>
        <spirit:displayName>VHDL Synthesis Wrapper</spirit:displayName>
        <spirit:envIdentifier>vhdlSource:vivado.xilinx.com:synthesis.wrapper</spirit:envIdentifier>
        <spirit:language>vhdl</spirit:language>
        <spirit:modelName>xdma_v4_1_12_blk_mem_64_reg_be</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_vhdlsynthesiswrapper_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Fri Jul 11 02:16:37 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:59dbb152</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_anylanguagebehavioralsimulation</spirit:name>
        <spirit:displayName>Simulation</spirit:displayName>
        <spirit:envIdentifier>:vivado.xilinx.com:simulation</spirit:envIdentifier>
        <spirit:modelName>blk_mem_gen_v8_4_4</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_anylanguagebehavioralsimulation_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Fri Jul 11 02:16:37 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:fbe8af69</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogsimulationwrapper</spirit:name>
        <spirit:displayName>Verilog Simulation Wrapper</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:simulation.wrapper</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>xdma_v4_1_12_blk_mem_64_reg_be</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsimulationwrapper_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Fri Jul 11 02:16:37 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:fbe8af69</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
    </spirit:views>
    <spirit:ports>
      <spirit:port>
        <spirit:name>clka</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.clka" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1)">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>rsta</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.rsta" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_RSTA&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>ena</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.ena" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_ENA&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>regcea</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.regcea" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_REGCEA&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>wea</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_WEA_WIDTH&apos;))-1">7</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.wea" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 3) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 4) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>addra</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_ADDRA_WIDTH&apos;))-1">8</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.addra" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1)">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>dina</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_WRITE_WIDTH_A&apos;))-1">71</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.dina" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 3) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 4) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>douta</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_READ_WIDTH_A&apos;))-1">71</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.douta" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>clkb</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.clkb" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 3) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>rstb</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.rstb" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 3) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_RSTB&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>enb</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.enb" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 3) and(spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_ENB&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>regceb</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.regceb" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 3) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_REGCEB&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>web</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_WEB_WIDTH&apos;))-1">7</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.web" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 3) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 4) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>addrb</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_ADDRB_WIDTH&apos;))-1">8</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.addrb" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 3) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>dinb</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_WRITE_WIDTH_B&apos;))-1">71</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.dinb" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 3) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 4) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>doutb</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_READ_WIDTH_B&apos;))-1">71</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.doutb" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 3) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>injectsbiterr</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.injectsbiterr" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_INJECTERR&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_INJECTERR&apos;)) != 2) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>injectdbiterr</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.injectdbiterr" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_INJECTERR&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_INJECTERR&apos;)) != 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>eccpipece</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.eccpipece" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;virtexu&apos; or spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;kintexu&apos; or spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;artixu&apos;) and spirit:decode(id(&apos;MODELPARAM_VALUE.C_EN_ECC_PIPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>sbiterr</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.sbiterr" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_ECC&apos;)) != 0) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_SOFTECC&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>dbiterr</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.dbiterr" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_ECC&apos;)) != 0) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_SOFTECC&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>rdaddrecc</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_ADDRB_WIDTH&apos;))-1">8</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.rdaddrecc" xilinx:dependency="((((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_ECC&apos;)) != 0) and id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;) != &apos;virtex5&apos;)) or ((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_SOFTECC&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) != 1)))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>sleep</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.sleep" xilinx:dependency="(((spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;virtexu&apos; or spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;kintexu&apos; or spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;artixu&apos;) or (spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;virtexuplushbm&apos;  or spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;virtexuplus58g&apos;or spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;virtexuplus&apos; or spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;kintexuplus&apos; or spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;zynquplus&apos;))and spirit:decode(id(&apos;MODELPARAM_VALUE.C_EN_SLEEP_PIN&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>deepsleep</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.deepsleep" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;virtexum&apos; or spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;zynque&apos;) and spirit:decode(id(&apos;MODELPARAM_VALUE.C_EN_DEEPSLEEP_PIN&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>shutdown</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.shutdown" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;virtexum&apos; or spirit:decode(id(&apos;MODELPARAM_VALUE.C_FAMILY&apos;)) = &apos;zynque&apos; ) and spirit:decode(id(&apos;MODELPARAM_VALUE.C_EN_SHUTDOWN_PIN&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>rsta_busy</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.rsta_busy" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_EN_SAFETY_CKT&apos;)) = 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>rstb_busy</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.rstb_busy" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 0) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_MEM_TYPE&apos;)) != 3) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_EN_SAFETY_CKT&apos;)) != 0))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_aclk</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0x0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_aclk" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_aresetn</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_aresetn" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_awid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_ID_WIDTH&apos;))-1">3</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_awid" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_AXI_ID&apos;)) = 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_awaddr</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">31</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_awaddr" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_awlen</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">7</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_awlen" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_TYPE&apos;)) =1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_awsize</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">2</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_awsize" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_TYPE&apos;)) =1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_SLAVE_TYPE&apos;)) = 0))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_awburst</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_awburst" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_TYPE&apos;)) =1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_SLAVE_TYPE&apos;)) = 0))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_awvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_awvalid" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_awready</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_awready" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_wdata</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_WRITE_WIDTH_A&apos;))-1">71</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_wdata" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_wstrb</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_WEA_WIDTH&apos;))-1">7</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_wstrb" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_wlast</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_wlast" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_TYPE&apos;))=1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_wvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_wvalid" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_wready</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_wready" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_bid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_ID_WIDTH&apos;))-1">3</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_bid" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_AXI_ID&apos;)) = 1 ))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_bresp</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_bresp" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_bvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_bvalid" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_bready</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_bready" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_arid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_ID_WIDTH&apos;))-1">3</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_arid" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_AXI_ID&apos;)) = 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_araddr</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">31</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_araddr" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_arlen</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">7</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_arlen" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_TYPE&apos;)) =1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_arsize</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">2</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_arsize" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_TYPE&apos;)) =1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_SLAVE_TYPE&apos;)) = 0))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_arburst</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_arburst" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_TYPE&apos;)) =1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_SLAVE_TYPE&apos;)) = 0))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_arvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_arvalid" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_arready</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_arready" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_rid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_ID_WIDTH&apos;))-1">3</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_rid" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_AXI_ID&apos;)) = 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_rdata</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_WRITE_WIDTH_B&apos;))-1">71</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_rdata" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_rresp</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_rresp" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_rlast</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_rlast" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1) and (spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXI_TYPE&apos;)) = 1))">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_rvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_rvalid" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_rready</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_rready" xilinx:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_injectsbiterr</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_injectsbiterr" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_INJECTERR&apos;)) = 1 or spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_INJECTERR&apos;)) = 3) and spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_injectdbiterr</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_injectdbiterr" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_INJECTERR&apos;)) = 2 or spirit:decode(id(&apos;MODELPARAM_VALUE.C_HAS_INJECTERR&apos;)) = 3) and spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_sbiterr</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_sbiterr" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_ECC&apos;)) = 1 or spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_SOFTECC&apos;)) = 1) and spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_dbiterr</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_dbiterr" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_ECC&apos;)) = 1 or spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_SOFTECC&apos;)) = 1) and spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axi_rdaddrecc</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_ADDRB_WIDTH&apos;))-1">8</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_vhdlsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_anylanguagebehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axi_rdaddrecc" xilinx:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_ECC&apos;)) = 1 or spirit:decode(id(&apos;MODELPARAM_VALUE.C_USE_SOFTECC&apos;)) = 1) and spirit:decode(id(&apos;MODELPARAM_VALUE.C_INTERFACE_TYPE&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
    </spirit:ports>
    <spirit:modelParameters>
      <spirit:modelParameter xsi:type="spirit:nameValueTypeType" spirit:dataType="STRING">
        <spirit:name>C_FAMILY</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_FAMILY">kintex7</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_XDEVICEFAMILY</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_XDEVICEFAMILY">kintex7</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_ELABORATION_DIR</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ELABORATION_DIR">./</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_INTERFACE_TYPE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_INTERFACE_TYPE">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_AXI_TYPE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_AXI_TYPE">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_AXI_SLAVE_TYPE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_AXI_SLAVE_TYPE">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_BRAM_BLOCK</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_BRAM_BLOCK">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_ENABLE_32BIT_ADDRESS</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ENABLE_32BIT_ADDRESS">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_CTRL_ECC_ALGO</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_CTRL_ECC_ALGO">NONE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_AXI_ID</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_AXI_ID">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_AXI_ID_WIDTH</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_AXI_ID_WIDTH">4</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_MEM_TYPE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MEM_TYPE">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_BYTE_SIZE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_BYTE_SIZE">9</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_ALGORITHM</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ALGORITHM">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_PRIM_TYPE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_PRIM_TYPE">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_LOAD_INIT_FILE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_LOAD_INIT_FILE">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_INIT_FILE_NAME</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_INIT_FILE_NAME">no_coe_file_loaded</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_INIT_FILE</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_INIT_FILE">xdma_v4_1_12_blk_mem_64_reg_be.mem</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_DEFAULT_DATA</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_DEFAULT_DATA">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_DEFAULT_DATA</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DEFAULT_DATA">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_RSTA</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_RSTA">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_RST_PRIORITY_A</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_RST_PRIORITY_A">CE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_RSTRAM_A</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_RSTRAM_A">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_INITA_VAL</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_INITA_VAL">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_ENA</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_ENA">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_REGCEA</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_REGCEA">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_BYTE_WEA</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_BYTE_WEA">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_WEA_WIDTH</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_WEA_WIDTH">8</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_WRITE_MODE_A</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_WRITE_MODE_A">READ_FIRST</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_WRITE_WIDTH_A</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_WRITE_WIDTH_A">72</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_READ_WIDTH_A</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_READ_WIDTH_A">72</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_WRITE_DEPTH_A</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_WRITE_DEPTH_A">512</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_READ_DEPTH_A</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_READ_DEPTH_A">512</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_ADDRA_WIDTH</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ADDRA_WIDTH">9</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_RSTB</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_RSTB">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_RST_PRIORITY_B</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_RST_PRIORITY_B">CE</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_RSTRAM_B</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_RSTRAM_B">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_INITB_VAL</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_INITB_VAL">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_ENB</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_ENB">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_REGCEB</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_REGCEB">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_BYTE_WEB</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_BYTE_WEB">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_WEB_WIDTH</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_WEB_WIDTH">8</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_WRITE_MODE_B</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_WRITE_MODE_B">READ_FIRST</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_WRITE_WIDTH_B</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_WRITE_WIDTH_B">72</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_READ_WIDTH_B</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_READ_WIDTH_B">72</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_WRITE_DEPTH_B</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_WRITE_DEPTH_B">512</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_READ_DEPTH_B</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_READ_DEPTH_B">512</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_ADDRB_WIDTH</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_ADDRB_WIDTH">9</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_MEM_OUTPUT_REGS_A</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_MEM_OUTPUT_REGS_A">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_MEM_OUTPUT_REGS_B</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_MEM_OUTPUT_REGS_B">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_MUX_OUTPUT_REGS_A</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_MUX_OUTPUT_REGS_A">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_MUX_OUTPUT_REGS_B</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_MUX_OUTPUT_REGS_B">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_MUX_PIPELINE_STAGES</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_MUX_PIPELINE_STAGES">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_SOFTECC_INPUT_REGS_A</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_SOFTECC_INPUT_REGS_A">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_SOFTECC_OUTPUT_REGS_B</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_SOFTECC_OUTPUT_REGS_B">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_SOFTECC</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_SOFTECC">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_ECC</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_ECC">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_EN_ECC_PIPE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_EN_ECC_PIPE">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_READ_LATENCY_A</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_READ_LATENCY_A">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_READ_LATENCY_B</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_READ_LATENCY_B">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_HAS_INJECTERR</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_HAS_INJECTERR">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_SIM_COLLISION_CHECK</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_SIM_COLLISION_CHECK">ALL</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_COMMON_CLK</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_COMMON_CLK">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_DISABLE_WARN_BHV_COLL</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DISABLE_WARN_BHV_COLL">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_EN_SLEEP_PIN</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_EN_SLEEP_PIN">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_USE_URAM</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_USE_URAM">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_EN_RDADDRA_CHG</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_EN_RDADDRA_CHG">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_EN_RDADDRB_CHG</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_EN_RDADDRB_CHG">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_EN_DEEPSLEEP_PIN</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_EN_DEEPSLEEP_PIN">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_EN_SHUTDOWN_PIN</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_EN_SHUTDOWN_PIN">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_EN_SAFETY_CKT</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_EN_SAFETY_CKT">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="INTEGER">
        <spirit:name>C_DISABLE_WARN_BHV_RANGE</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_DISABLE_WARN_BHV_RANGE">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_COUNT_36K_BRAM</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_COUNT_36K_BRAM">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_COUNT_18K_BRAM</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_COUNT_18K_BRAM">0</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="STRING">
        <spirit:name>C_EST_POWER_SUMMARY</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_EST_POWER_SUMMARY">Estimated Power for IP     :     7.638025 mW</spirit:value>
      </spirit:modelParameter>
    </spirit:modelParameters>
  </spirit:model>
  <spirit:choices>
    <spirit:choice>
      <spirit:name>choice_list_5453281d</spirit:name>
      <spirit:enumeration>Native</spirit:enumeration>
      <spirit:enumeration>AXI4</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_list_5f2d7eda</spirit:name>
      <spirit:enumeration>16kx1</spirit:enumeration>
      <spirit:enumeration>8kx2</spirit:enumeration>
      <spirit:enumeration>4kx4</spirit:enumeration>
      <spirit:enumeration>2kx9</spirit:enumeration>
      <spirit:enumeration>1kx18</spirit:enumeration>
      <spirit:enumeration>512x36</spirit:enumeration>
      <spirit:enumeration>256x72</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_list_6e3ded9c</spirit:name>
      <spirit:enumeration>0</spirit:enumeration>
      <spirit:enumeration>1</spirit:enumeration>
      <spirit:enumeration>2</spirit:enumeration>
      <spirit:enumeration>3</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_list_89a27b2f</spirit:name>
      <spirit:enumeration>8</spirit:enumeration>
      <spirit:enumeration>9</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_list_bdf7387e</spirit:name>
      <spirit:enumeration>BRAM</spirit:enumeration>
      <spirit:enumeration>URAM</spirit:enumeration>
      <spirit:enumeration>AUTO</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_list_c8df20f0</spirit:name>
      <spirit:enumeration>NONE</spirit:enumeration>
      <spirit:enumeration>ECCH32-7</spirit:enumeration>
      <spirit:enumeration>ECCH64-8</spirit:enumeration>
      <spirit:enumeration>ECCHSIAO32-7</spirit:enumeration>
      <spirit:enumeration>ECCHSIAO64-8</spirit:enumeration>
      <spirit:enumeration>ECCHSIAO128-9</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_list_f92be8b1</spirit:name>
      <spirit:enumeration>18</spirit:enumeration>
      <spirit:enumeration>36</spirit:enumeration>
      <spirit:enumeration>72</spirit:enumeration>
      <spirit:enumeration>144</spirit:enumeration>
      <spirit:enumeration>288</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_07c32259</spirit:name>
      <spirit:enumeration spirit:text="Write First">WRITE_FIRST</spirit:enumeration>
      <spirit:enumeration spirit:text="Read First">READ_FIRST</spirit:enumeration>
      <spirit:enumeration spirit:text="No Change">NO_CHANGE</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_27c66b0d</spirit:name>
      <spirit:enumeration spirit:text="Stand Alone">Stand_Alone</spirit:enumeration>
      <spirit:enumeration spirit:text="BRAM Controller">BRAM_Controller</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_2adcaf32</spirit:name>
      <spirit:enumeration spirit:text="Synchronous">SYNC</spirit:enumeration>
      <spirit:enumeration spirit:text="Asynchronous">ASYNC</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_2d73cdeb</spirit:name>
      <spirit:enumeration spirit:text="Always Enabled">Always_Enabled</spirit:enumeration>
      <spirit:enumeration spirit:text="Use ENB Pin">Use_ENB_Pin</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_3949ecbf</spirit:name>
      <spirit:enumeration spirit:text="Always Enabled">Always_Enabled</spirit:enumeration>
      <spirit:enumeration spirit:text="Use ENA Pin">Use_ENA_Pin</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_3e9ce7ae</spirit:name>
      <spirit:enumeration spirit:text="Minimum Area">Minimum_Area</spirit:enumeration>
      <spirit:enumeration spirit:text="Low Power">Low_Power</spirit:enumeration>
      <spirit:enumeration spirit:text="Fixed Primitives">Fixed_Primitives</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_44b9b2d1</spirit:name>
      <spirit:enumeration spirit:text="All">ALL</spirit:enumeration>
      <spirit:enumeration spirit:text="None">NONE</spirit:enumeration>
      <spirit:enumeration spirit:text="Warning Only">WARNING_ONLY</spirit:enumeration>
      <spirit:enumeration spirit:text="Generate X-Only">GENERATE_X_ONLY</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_63de7f78</spirit:name>
      <spirit:enumeration spirit:text="CE (Latch or Register Enable)">CE</spirit:enumeration>
      <spirit:enumeration spirit:text="SR (Set Reset)">SR</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_716d2fba</spirit:name>
      <spirit:enumeration spirit:text="Single Bit Error Injection">Single_Bit_Error_Injection</spirit:enumeration>
      <spirit:enumeration spirit:text="Double Bit Error Injection">Double_Bit_Error_Injection</spirit:enumeration>
      <spirit:enumeration spirit:text="Single and Double Bit Error Injection">Single_and_Double_Bit_Error_Injection</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_b91edaa2</spirit:name>
      <spirit:enumeration spirit:text="Memory Slave">Memory_Slave</spirit:enumeration>
      <spirit:enumeration spirit:text="Peripheral Slave">Peripheral_Slave</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_c1013cbe</spirit:name>
      <spirit:enumeration spirit:text="No ECC">No_ECC</spirit:enumeration>
      <spirit:enumeration spirit:text="Soft ECC">Soft_ECC</spirit:enumeration>
      <spirit:enumeration spirit:text="BuiltIn ECC">BuiltIn_ECC</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_e4c322cb</spirit:name>
      <spirit:enumeration spirit:text="AXI4">AXI4_Full</spirit:enumeration>
      <spirit:enumeration spirit:text="AXI4 Lite">AXI4_Lite</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_e932d933</spirit:name>
      <spirit:enumeration spirit:text="Single Port RAM">Single_Port_RAM</spirit:enumeration>
      <spirit:enumeration spirit:text="Simple Dual Port RAM">Simple_Dual_Port_RAM</spirit:enumeration>
      <spirit:enumeration spirit:text="True Dual Port RAM">True_Dual_Port_RAM</spirit:enumeration>
      <spirit:enumeration spirit:text="Single Port ROM">Single_Port_ROM</spirit:enumeration>
      <spirit:enumeration spirit:text="Dual Port ROM">Dual_Port_ROM</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_eab86dfe</spirit:name>
      <spirit:enumeration spirit:text="Read First">READ_FIRST</spirit:enumeration>
    </spirit:choice>
  </spirit:choices>
  <spirit:fileSets>
    <spirit:fileSet>
      <spirit:name>xilinx_vhdlsynthesis_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>xdma_v4_1_12_blk_mem_64_reg_be_ooc.xdc</spirit:name>
        <spirit:userFileType>xdc</spirit:userFileType>
        <spirit:userFileType>USED_IN_implementation</spirit:userFileType>
        <spirit:userFileType>USED_IN_out_of_context</spirit:userFileType>
        <spirit:userFileType>USED_IN_synthesis</spirit:userFileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>hdl/blk_mem_gen_v8_4_vhsyn_rfs.vhd</spirit:name>
        <spirit:fileType>vhdlSource</spirit:fileType>
        <spirit:logicalName>blk_mem_gen_v8_4_4</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_vhdlsynthesiswrapper_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>synth/xdma_v4_1_12_blk_mem_64_reg_be.vhd</spirit:name>
        <spirit:fileType>vhdlSource</spirit:fileType>
        <spirit:logicalName>blk_mem_gen_v8_4_4</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_anylanguagebehavioralsimulation_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>simulation/blk_mem_gen_v8_4.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:logicalName>blk_mem_gen_v8_4_4</spirit:logicalName>
        <spirit:exportedName>blk_mem_gen_v8_4_4</spirit:exportedName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsimulationwrapper_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>sim/xdma_v4_1_12_blk_mem_64_reg_be.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:logicalName>xil_defaultlib</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
  </spirit:fileSets>
  <spirit:description>The Xilinx LogiCORE IP Block Memory Generator replaces the Dual Port Block Memory and Single Port Block Memory LogiCOREs, but is not a direct drop-in replacement.  It should be used in all new Xilinx designs. The core supports RAM and ROM functions over a wide range of widths and depths. Use this core to generate block memories with symmetric or asymmetric read and write port widths, as well as cores which can perform simultaneous write operations to separate locations, and simultaneous read operations from the same location. For more information on differences in interface and feature support between this core and the Dual Port Block Memory and Single Port Block Memory LogiCOREs, please consult the data sheet.</spirit:description>
  <spirit:parameters>
    <spirit:parameter>
      <spirit:name>Component_Name</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Component_Name" spirit:order="1">xdma_v4_1_12_blk_mem_64_reg_be</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Component_Name">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Interface_Type</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Interface_Type" spirit:choiceRef="choice_list_5453281d" spirit:order="2">Native</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Interface_Type">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>AXI_Type</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.AXI_Type" spirit:choiceRef="choice_pairs_e4c322cb" spirit:order="4">AXI4_Full</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.AXI_Type">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>AXI_Slave_Type</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.AXI_Slave_Type" spirit:choiceRef="choice_pairs_b91edaa2" spirit:order="5">Memory_Slave</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.AXI_Slave_Type">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Use_AXI_ID</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Use_AXI_ID" spirit:order="6">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Use_AXI_ID">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>AXI_ID_Width</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.AXI_ID_Width" spirit:order="7" spirit:minimum="1" spirit:maximum="16" spirit:rangeType="long">4</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.AXI_ID_Width">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Memory_Type</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Memory_Type" spirit:choiceRef="choice_pairs_e932d933" spirit:order="8">Simple_Dual_Port_RAM</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Memory_Type">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>PRIM_type_to_Implement</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.PRIM_type_to_Implement" spirit:choiceRef="choice_list_bdf7387e" spirit:order="8.001">BRAM</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.PRIM_type_to_Implement">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Enable_32bit_Address</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Enable_32bit_Address" spirit:order="9">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Enable_32bit_Address">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ecctype</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.ecctype" spirit:choiceRef="choice_pairs_c1013cbe" spirit:order="10">No_ECC</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.ecctype">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>ECC</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.ECC" spirit:order="11">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.ECC">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>softecc</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.softecc" spirit:order="12">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.softecc">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>EN_SLEEP_PIN</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.EN_SLEEP_PIN" spirit:order="13">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.EN_SLEEP_PIN">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>EN_DEEPSLEEP_PIN</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.EN_DEEPSLEEP_PIN" spirit:order="14">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.EN_DEEPSLEEP_PIN">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>EN_SHUTDOWN_PIN</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.EN_SHUTDOWN_PIN" spirit:order="15">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.EN_SHUTDOWN_PIN">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>EN_ECC_PIPE</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.EN_ECC_PIPE" spirit:order="16">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.EN_ECC_PIPE">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>RD_ADDR_CHNG_A</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.RD_ADDR_CHNG_A" spirit:order="17">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.RD_ADDR_CHNG_A">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>RD_ADDR_CHNG_B</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.RD_ADDR_CHNG_B" spirit:order="18">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.RD_ADDR_CHNG_B">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Use_Error_Injection_Pins</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Use_Error_Injection_Pins" spirit:order="13">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Use_Error_Injection_Pins">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Error_Injection_Type</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Error_Injection_Type" spirit:choiceRef="choice_pairs_716d2fba" spirit:order="14">Single_Bit_Error_Injection</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Error_Injection_Type">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Use_Byte_Write_Enable</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Use_Byte_Write_Enable" spirit:order="15">true</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Use_Byte_Write_Enable">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Byte_Size</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Byte_Size" spirit:choiceRef="choice_list_89a27b2f" spirit:order="16">9</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Byte_Size">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Algorithm</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Algorithm" spirit:choiceRef="choice_pairs_3e9ce7ae" spirit:order="20">Minimum_Area</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Algorithm">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Primitive</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Primitive" spirit:choiceRef="choice_list_5f2d7eda" spirit:order="21">8kx2</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Primitive">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Assume_Synchronous_Clk</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Assume_Synchronous_Clk" spirit:order="17">true</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Assume_Synchronous_Clk">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Write_Width_A</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.Write_Width_A" spirit:order="18" spirit:minimum="9" spirit:maximum="4608" spirit:rangeType="long">72</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Write_Width_A">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Write_Depth_A</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.Write_Depth_A" spirit:order="19" spirit:minimum="2" spirit:maximum="1048576" spirit:rangeType="long">512</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Write_Depth_A">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Read_Width_A</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Read_Width_A" spirit:choiceRef="choice_list_f92be8b1" spirit:order="22">72</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Read_Width_A">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Operating_Mode_A</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Operating_Mode_A" spirit:choiceRef="choice_pairs_07c32259" spirit:order="23">READ_FIRST</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Operating_Mode_A">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Enable_A</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Enable_A" spirit:choiceRef="choice_pairs_3949ecbf" spirit:order="24">Use_ENA_Pin</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Enable_A">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Write_Width_B</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Write_Width_B" spirit:choiceRef="choice_list_f92be8b1" spirit:order="25">72</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Write_Width_B">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Read_Width_B</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Read_Width_B" spirit:choiceRef="choice_list_f92be8b1" spirit:order="26">72</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Read_Width_B">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Operating_Mode_B</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Operating_Mode_B" spirit:choiceRef="choice_pairs_eab86dfe" spirit:order="27">READ_FIRST</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Operating_Mode_B">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Enable_B</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Enable_B" spirit:choiceRef="choice_pairs_2d73cdeb" spirit:order="28">Use_ENB_Pin</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Enable_B">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Register_PortA_Output_of_Memory_Primitives</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Register_PortA_Output_of_Memory_Primitives" spirit:order="29">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Register_PortA_Output_of_Memory_Primitives">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Register_PortA_Output_of_Memory_Core</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Register_PortA_Output_of_Memory_Core" spirit:order="30">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Register_PortA_Output_of_Memory_Core">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Use_REGCEA_Pin</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Use_REGCEA_Pin" spirit:order="31">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Use_REGCEA_Pin">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Register_PortB_Output_of_Memory_Primitives</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Register_PortB_Output_of_Memory_Primitives" spirit:order="32">true</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Register_PortB_Output_of_Memory_Primitives">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Register_PortB_Output_of_Memory_Core</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Register_PortB_Output_of_Memory_Core" spirit:order="33">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Register_PortB_Output_of_Memory_Core">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Use_REGCEB_Pin</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Use_REGCEB_Pin" spirit:order="34">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Use_REGCEB_Pin">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>register_porta_input_of_softecc</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.register_porta_input_of_softecc" spirit:order="35">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.register_porta_input_of_softecc">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>register_portb_output_of_softecc</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.register_portb_output_of_softecc" spirit:order="36">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.register_portb_output_of_softecc">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Pipeline_Stages</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Pipeline_Stages" spirit:choiceRef="choice_list_6e3ded9c" spirit:order="37">0</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Pipeline_Stages">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Load_Init_File</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Load_Init_File" spirit:order="38">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Load_Init_File">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Coe_File</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Coe_File" spirit:order="39">no_coe_file_loaded</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Coe_File">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Fill_Remaining_Memory_Locations</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Fill_Remaining_Memory_Locations" spirit:order="40">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Fill_Remaining_Memory_Locations">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Remaining_Memory_Locations</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Remaining_Memory_Locations" spirit:order="41">0</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Remaining_Memory_Locations">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Use_RSTA_Pin</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Use_RSTA_Pin" spirit:order="42">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Use_RSTA_Pin">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Reset_Memory_Latch_A</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Reset_Memory_Latch_A" spirit:order="43">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Reset_Memory_Latch_A">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Reset_Priority_A</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Reset_Priority_A" spirit:choiceRef="choice_pairs_63de7f78" spirit:order="44">CE</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Reset_Priority_A">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Output_Reset_Value_A</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Output_Reset_Value_A" spirit:order="45">0</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Output_Reset_Value_A">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Use_RSTB_Pin</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Use_RSTB_Pin" spirit:order="46">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Use_RSTB_Pin">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Reset_Memory_Latch_B</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Reset_Memory_Latch_B" spirit:order="47">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Reset_Memory_Latch_B">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Reset_Priority_B</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Reset_Priority_B" spirit:choiceRef="choice_pairs_63de7f78" spirit:order="48">CE</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Reset_Priority_B">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Output_Reset_Value_B</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Output_Reset_Value_B" spirit:order="49">0</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Output_Reset_Value_B">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Reset_Type</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Reset_Type" spirit:choiceRef="choice_pairs_2adcaf32" spirit:order="50">SYNC</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Reset_Type">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Additional_Inputs_for_Power_Estimation</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Additional_Inputs_for_Power_Estimation" spirit:order="51">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Additional_Inputs_for_Power_Estimation">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Port_A_Clock</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.Port_A_Clock" spirit:order="52" spirit:minimum="0" spirit:maximum="800" spirit:rangeType="long">100</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Port_A_Clock">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Port_A_Write_Rate</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.Port_A_Write_Rate" spirit:order="53" spirit:minimum="0" spirit:maximum="100" spirit:rangeType="long">50</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Port_A_Write_Rate">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Port_B_Clock</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.Port_B_Clock" spirit:order="54" spirit:minimum="0" spirit:maximum="800" spirit:rangeType="long">100</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Port_B_Clock">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Port_B_Write_Rate</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.Port_B_Write_Rate" spirit:order="55" spirit:minimum="0" spirit:maximum="100" spirit:rangeType="long">0</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Port_B_Write_Rate">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Port_A_Enable_Rate</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.Port_A_Enable_Rate" spirit:order="56" spirit:minimum="0" spirit:maximum="100" spirit:rangeType="long">100</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Port_A_Enable_Rate">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Port_B_Enable_Rate</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.Port_B_Enable_Rate" spirit:order="57" spirit:minimum="0" spirit:maximum="100" spirit:rangeType="long">100</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Port_B_Enable_Rate">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Collision_Warnings</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Collision_Warnings" spirit:choiceRef="choice_pairs_44b9b2d1" spirit:order="58">ALL</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Collision_Warnings">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Disable_Collision_Warnings</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Disable_Collision_Warnings" spirit:order="59">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Disable_Collision_Warnings">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Disable_Out_of_Range_Warnings</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.Disable_Out_of_Range_Warnings" spirit:order="60">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.Disable_Out_of_Range_Warnings">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>use_bram_block</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.use_bram_block" spirit:choiceRef="choice_pairs_27c66b0d" spirit:order="3">Stand_Alone</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.use_bram_block">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>MEM_FILE</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.MEM_FILE" spirit:order="61">no_mem_loaded</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.MEM_FILE">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>CTRL_ECC_ALGO</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.CTRL_ECC_ALGO" spirit:choiceRef="choice_list_c8df20f0" spirit:order="62">NONE</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.CTRL_ECC_ALGO">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>EN_SAFETY_CKT</spirit:name>
      <spirit:value spirit:format="bool" spirit:resolve="user" spirit:id="PARAM_VALUE.EN_SAFETY_CKT" spirit:order="63">false</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.EN_SAFETY_CKT">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>READ_LATENCY_A</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.READ_LATENCY_A" spirit:order="63" spirit:minimum="1" spirit:maximum="128" spirit:rangeType="long">1</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.READ_LATENCY_A">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>READ_LATENCY_B</spirit:name>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.READ_LATENCY_B" spirit:order="63" spirit:minimum="1" spirit:maximum="128" spirit:rangeType="long">1</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.READ_LATENCY_B">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
  </spirit:parameters>
  <spirit:vendorExtensions>
    <xilinx:coreExtensions>
      <xilinx:displayName>Block Memory Generator</xilinx:displayName>
      <xilinx:xpmLibraries>
        <xilinx:xpmLibrary>XPM_MEMORY</xilinx:xpmLibrary>
      </xilinx:xpmLibraries>
      <xilinx:coreRevision>4</xilinx:coreRevision>
      <xilinx:configElementInfos>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.ADDR_WIDTH" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.ARUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.AWUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.BUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.DATA_WIDTH" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_BRESP" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_BURST" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_CACHE" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_LOCK" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_PROT" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_QOS" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_REGION" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_RRESP" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.HAS_WSTRB" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.PROTOCOL" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.RUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXILITE_SLAVE_S_AXI.WUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.ADDR_WIDTH" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.ARUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.AWUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.BUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.DATA_WIDTH" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_BRESP" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_BURST" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_CACHE" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_LOCK" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_PROT" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_QOS" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_REGION" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_RRESP" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.HAS_WSTRB" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.PROTOCOL" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.RUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.AXI_SLAVE_S_AXI.WUSER_WIDTH" xilinx:valueSource="constant"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Assume_Synchronous_Clk" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Byte_Size" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Enable_A" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Enable_B" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Memory_Type" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Operating_Mode_A" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Operating_Mode_B" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Port_B_Clock" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Port_B_Enable_Rate" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Read_Width_A" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Read_Width_B" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Register_PortA_Output_of_Memory_Primitives" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Register_PortB_Output_of_Memory_Primitives" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Use_Byte_Write_Enable" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Write_Depth_A" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Write_Width_A" xilinx:valueSource="propagated"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Write_Width_B" xilinx:valueSource="propagated"/>
      </xilinx:configElementInfos>
    </xilinx:coreExtensions>
    <xilinx:packagingInfo>
      <xilinx:xilinxVersion>2021.1</xilinx:xilinxVersion>
      <xilinx:checksum xilinx:scope="busInterfaces" xilinx:value="52ed1045"/>
      <xilinx:checksum xilinx:scope="memoryMaps" xilinx:value="a1d35893"/>
      <xilinx:checksum xilinx:scope="fileGroups" xilinx:value="4ba6d99f"/>
      <xilinx:checksum xilinx:scope="ports" xilinx:value="3031f2fd"/>
      <xilinx:checksum xilinx:scope="hdlParameters" xilinx:value="9c3516e3"/>
      <xilinx:checksum xilinx:scope="parameters" xilinx:value="e6bad347"/>
    </xilinx:packagingInfo>
  </spirit:vendorExtensions>
</spirit:component>
