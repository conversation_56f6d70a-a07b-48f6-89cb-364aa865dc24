SET_FLAG MODE BATCH
SET_FLAG STANDALONE_MODE TRUE
SET_PREFERENCE ipi_mode no
SET_PREFERENCE is_ip_locked false
SET_PREFERENCE devicefamily kintex7
SET_PREFERENCE device xc7k325t
SET_PREFERENCE speedgrade -2
SET_PREFERENCE package ffg900
SET_PREFERENCE verilogsim true
SET_PREFERENCE vhdlsim false
SET_PREFERENCE designentry Verilog
SET_PREFERENCE outputdirectory f:/NSSC/reference/mk7100/TJY/project_1/project_1.gen/sources_1/ip/mig_7series_0/_tmp/
SET_PREFERENCE subworkingdirectory f:/NSSC/reference/mk7100/TJY/project_1/project_1.gen/sources_1/ip/mig_7series_0/_tmp/
SET_PREFERENCE flowvendor Other
SET_PREFERENCE tool vivado
SET_PREFERENCE compnamestatus 0
SET_PARAMETER component_name mig_7series_0
SET_PARAMETER xml_input_file F:/NSSC/reference/mk7100/TJY/project_1/project_1.srcs/sources_1/ip/mig_7series_0/mig_a.prj
SET_PARAMETER data_dir_path g:/Devtools/xilinx_2021.1/Vivado/2021.1/data/ip/xilinx/mig_7series_v4_2
SET_CORE_NAME Memory Interface Generator (MIG 7 Series)
SET_CORE_VERSION 4.2
SET_CORE_VLNV xilinx.com:ip:mig_7series:4.2
SET_CORE_PATH g:/Devtools/xilinx_2021.1/Vivado/2021.1/data/ip/xilinx/mig_7series_v4_2
SET_CORE_DATASHEET g:/Devtools/xilinx_2021.1/Vivado/2021.1/data/ip/xilinx/mig_7series_v4_2/data/docs/ds176_7series_MIS.pdf
