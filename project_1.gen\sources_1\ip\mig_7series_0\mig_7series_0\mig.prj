﻿<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project NoOfControllers="1">

  <!-- IMPORTANT: This is an internal file that has been generated by the MIG software. Any direct editing or changes made to this file may result in unpredictable behavior or data corruption. It is strongly advised that users do not edit the contents of this file. Re-run the MIG GUI with the required settings if any of the options provided below need to be altered. -->

  <ModuleName>mig_7series_0</ModuleName>

  <dci_inouts_inputs>1</dci_inouts_inputs>

  <dci_inputs>1</dci_inputs>

  <Debug_En>OFF</Debug_En>

  <DataDepth_En>1024</DataDepth_En>

  <LowPower_En>ON</LowPower_En>

  <XADC_En>Enabled</XADC_En>

  <TargetFPGA>xc7k325t-ffg900/-2</TargetFPGA>

  <Version>4.2</Version>

  <SystemClock>No Buffer</SystemClock>

  <ReferenceClock>Use System Clock</ReferenceClock>

  <SysResetPolarity>ACTIVE LOW</SysResetPolarity>

  <BankSelectionFlag>FALSE</BankSelectionFlag>

  <InternalVref>0</InternalVref>

  <dci_hr_inouts_inputs>50 Ohms</dci_hr_inouts_inputs>

  <dci_cascade>0</dci_cascade>

  <Controller number="0">
    <MemoryDevice>DDR3_SDRAM/Components/MT41J256m16XX-125</MemoryDevice>
    <TimePeriod>2000</TimePeriod>
    <VccAuxIO>1.8V</VccAuxIO>
    <PHYRatio>4:1</PHYRatio>
    <InputClkFreq>200</InputClkFreq>
    <UIExtraClocks>0</UIExtraClocks>
    <MMCM_VCO>1000</MMCM_VCO>
    <MMCMClkOut0> 1.000</MMCMClkOut0>
    <MMCMClkOut1>1</MMCMClkOut1>
    <MMCMClkOut2>1</MMCMClkOut2>
    <MMCMClkOut3>1</MMCMClkOut3>
    <MMCMClkOut4>1</MMCMClkOut4>
    <DataWidth>72</DataWidth>
    <DeepMemory>1</DeepMemory>
    <DataMask>0</DataMask>
    <ECC>Enabled</ECC>
    <Ordering>Normal</Ordering>
    <BankMachineCnt>4</BankMachineCnt>
    <CustomPart>FALSE</CustomPart>
    <NewPartName></NewPartName>
    <RowAddress>15</RowAddress>
    <ColAddress>10</ColAddress>
    <BankAddress>3</BankAddress>
    <MemoryVoltage>1.5V</MemoryVoltage>
    <UserMemoryAddressMap>BANK_ROW_COLUMN</UserMemoryAddressMap>
    <PinSelection>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AG9" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[0]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AA10" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[10]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AE11" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[11]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AJ11" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[12]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AD11" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[13]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AC11" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[14]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AF11" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[1]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AK10" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[2]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AD9" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[3]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AD12" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[4]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AE8" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[5]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AC12" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[6]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AD8" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[7]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AA13" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[8]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AK11" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_addr[9]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="Y10" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_ba[0]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AH11" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_ba[1]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AC10" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_ba[2]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AK9" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_cas_n"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15" PADName="AH10" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_ck_n[0]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15" PADName="AG10" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_ck_p[0]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AA11" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_cke[0]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AE9" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_cs_n[0]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AA15" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[0]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AB19" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[10]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AD16" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[11]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AB18" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[12]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AB17" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[13]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AA18" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[14]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AD17" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[15]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AK15" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[16]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AK16" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[17]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AG15" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[18]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AH17" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[19]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AB15" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[1]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AH15" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[20]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AE16" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[21]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AG14" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[22]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AJ17" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[23]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AJ19" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[24]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AG18" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[25]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AE19" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[26]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AF17" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[27]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AK19" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[28]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AF18" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[29]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="Y15" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[2]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AD19" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[30]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AG19" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[31]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AJ2" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[32]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AK3" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[33]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AK1" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[34]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AH4" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[35]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AH2" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[36]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AH5" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[37]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AJ1" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[38]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AJ3" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[39]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AA16" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[3]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AK8" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[40]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AK6" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[41]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AF7" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[42]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AK4" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[43]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AG7" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[44]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AK5" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[45]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AJ8" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[46]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AJ6" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[47]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AC1" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[48]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AD4" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[49]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AD14" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[4]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AC2" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[50]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AD6" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[51]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AC4" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[52]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AC7" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[53]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AD3" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[54]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AC5" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[55]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AG5" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[56]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AF1" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[57]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AF6" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[58]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AE3" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[59]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="Y16" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[5]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AE5" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[60]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AE1" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[61]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AF5" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[62]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AE4" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[63]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AK13" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[64]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AG13" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[65]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AG12" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[66]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AF12" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[67]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AJ13" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[68]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AH12" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[69]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AC14" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[6]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AJ12" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[70]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AK14" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[71]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AA17" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[7]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AC19" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[8]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15_T_DCI" PADName="AE18" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dq[9]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AE15" SLEW="FAST" VCCAUX_IO="" name="ddr3_dqm[0]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AC17" SLEW="FAST" VCCAUX_IO="" name="ddr3_dqm[1]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AF15" SLEW="FAST" VCCAUX_IO="" name="ddr3_dqm[2]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AH19" SLEW="FAST" VCCAUX_IO="" name="ddr3_dqm[3]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AH6" SLEW="FAST" VCCAUX_IO="" name="ddr3_dqm[4]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AF8" SLEW="FAST" VCCAUX_IO="" name="ddr3_dqm[5]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AE6" SLEW="FAST" VCCAUX_IO="" name="ddr3_dqm[6]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AF2" SLEW="FAST" VCCAUX_IO="" name="ddr3_dqm[7]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AC15" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_n[0]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="Y18" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_n[1]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AJ16" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_n[2]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AK18" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_n[3]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AH1" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_n[4]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AJ7" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_n[5]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AD1" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_n[6]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AG3" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_n[7]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AJ14" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_n[8]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AC16" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_p[0]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="Y19" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_p[1]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AH16" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_p[2]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AJ18" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_p[3]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AG2" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_p[4]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AH7" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_p[5]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AD2" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_p[6]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AG4" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_p[7]"/>
      <Pin IN_TERM="" IOSTANDARD="DIFF_SSTL15_T_DCI" PADName="AH14" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_dqs_p[8]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AB10" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_odt[0]"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AH9" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_ras_n"/>
      <Pin IN_TERM="" IOSTANDARD="LVCMOS15" PADName="Y11" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_reset_n"/>
      <Pin IN_TERM="" IOSTANDARD="SSTL15" PADName="AJ9" SLEW="FAST" VCCAUX_IO="NORMAL" name="ddr3_we_n"/>
    </PinSelection>
    <System_Control>
      <Pin Bank="Select Bank" PADName="No connect" name="sys_rst"/>
      <Pin Bank="Select Bank" PADName="No connect" name="init_calib_complete"/>
      <Pin Bank="Select Bank" PADName="No connect" name="tg_compare_error"/>
    </System_Control>
    <TimingParameters>
      <Parameters tcke="5" tfaw="40" tras="35" trcd="13.75" trefi="7.8" trfc="260" trp="13.75" trrd="7.5" trtp="7.5" twtr="7.5"/>
    </TimingParameters>
    <mrBurstLength name="Burst Length">8 - Fixed</mrBurstLength>
    <mrBurstType name="Read Burst Type and Length">Sequential</mrBurstType>
    <mrCasLatency name="CAS Latency">7</mrCasLatency>
    <mrMode name="Mode">Normal</mrMode>
    <mrDllReset name="DLL Reset">No</mrDllReset>
    <mrPdMode name="DLL control for precharge PD">Slow Exit</mrPdMode>
    <emrDllEnable name="DLL Enable">Enable</emrDllEnable>
    <emrOutputDriveStrength name="Output Driver Impedance Control">RZQ/7</emrOutputDriveStrength>
    <emrMirrorSelection name="Address Mirroring">Disable</emrMirrorSelection>
    <emrCSSelection name="Controller Chip Select Pin">Enable</emrCSSelection>
    <emrRTT name="RTT (nominal) - On Die Termination (ODT)">RZQ/4</emrRTT>
    <emrPosted name="Additive Latency (AL)">0</emrPosted>
    <emrOCD name="Write Leveling Enable">Disabled</emrOCD>
    <emrDQS name="TDQS enable">Enabled</emrDQS>
    <emrRDQS name="Qoff">Output Buffer Enabled</emrRDQS>
    <mr2PartialArraySelfRefresh name="Partial-Array Self Refresh">Full Array</mr2PartialArraySelfRefresh>
    <mr2CasWriteLatency name="CAS write latency">6</mr2CasWriteLatency>
    <mr2AutoSelfRefresh name="Auto Self Refresh">Enabled</mr2AutoSelfRefresh>
    <mr2SelfRefreshTempRange name="High Temparature Self Refresh Rate">Normal</mr2SelfRefreshTempRange>
    <mr2RTTWR name="RTT_WR - Dynamic On Die Termination (ODT)">Dynamic ODT off</mr2RTTWR>
    <PortInterface>NATIVE</PortInterface>
  </Controller>

</Project>
