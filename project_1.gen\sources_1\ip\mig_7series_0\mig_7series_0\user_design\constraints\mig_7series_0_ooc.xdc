###################################################################################################
## This constraints file contains default clock frequencies to be used during creation of a 
## Synthesis Design Checkpoint (DCP). For best results the frequencies should be modified 
## to match the target frequencies. 
## This constraints file is not used in top-down/global synthesis (not the default flow of Vivado).
###################################################################################################


##################################################################################################

## 

##  Xilinx, Inc. 2010            www.xilinx.com 

##  Fri Jul 11 10:16:34 2025


##  Generated by MIG Version 4.2

##  

##################################################################################################

##  File name :       mig_7series_0.xdc

##  Details :     Constraints file

##                    FPGA Family:       KINTEX7

##                    FPGA Part:         XC7K325T-FFG900

##                    Speedgrade:        -2

##                    Design Entry:      VERILOG

##                    Frequency:         500 MHz

##                    Time Period:       2000 ps

##################################################################################################



##################################################################################################

## Controller 0

## Memory Device: DDR3_SDRAM->Components->MT41J256m16XX-125

## Data Width: 72

## Time Period: 2000

## Data Mask: 0

##################################################################################################



create_clock -period 5 [get_ports sys_clk_i]

          