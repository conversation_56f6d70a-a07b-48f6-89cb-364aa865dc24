Files in PAR folder :



* "example_top.xdc" file is the constraint file for the design. This is used

  by Vivado. It has clock constraints, location constraints, IO standards 

  and false path/SLICE constraints if any.



* LTX/probe file is required when programming BIT file to FPGA as it contains

  the information of debug signals like signal name and position with respect

  to ILA/VIO core. The probe file (debug_nets.ltx) is auto generated by 

  vivado tool and is found in <project_name>.runs/impl_1/debug_nets.ltx



compatible_ucf folder:



* MIG outputs this folder only when Pin Compatible FPGAs are checked in GUI

  (Pin Compatible FPGAs page in GUI). It generates the XDC files for all

  the Compatible FPGAs selected in GUI. If you want to switch to any of the

  Compatible FPGAs follow the steps mentioned below.



