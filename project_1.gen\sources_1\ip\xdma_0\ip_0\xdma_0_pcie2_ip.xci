<?xml version="1.0" encoding="UTF-8"?>
<spirit:design xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>xilinx.com</spirit:vendor>
  <spirit:library>xci</spirit:library>
  <spirit:name>unknown</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:componentInstances>
    <spirit:componentInstance>
      <spirit:instanceName>xdma_0_pcie2_ip</spirit:instanceName>
      <spirit:componentRef spirit:vendor="xilinx.com" spirit:library="ip" spirit:name="pcie_7x" spirit:version="3.3"/>
      <spirit:configurableElementValues>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLK.SYS_CLK.ASSOCIATED_BUSIF"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLK.SYS_CLK.ASSOCIATED_RESET"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLK.SYS_CLK.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLK.SYS_CLK.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLK.SYS_CLK.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLK.SYS_CLK.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLK.SYS_CLK.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLK.USER_CLK_OUT.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLK.USER_CLK_OUT.FREQ_TOLERANCE_HZ">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLK.USER_CLK_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.CLK.USER_CLK_OUT.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.HAS_TKEEP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.HAS_TLAST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.HAS_TREADY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.HAS_TSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.LAYERED_METADATA">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.TDATA_NUM_BYTES">16</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.TDEST_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.TID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.TUSER_WIDTH">22</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RST.SYS_RST_N.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.RST.USER_RESET_OUT.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.CLK_DOMAIN"/>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.FREQ_HZ">100000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.HAS_TKEEP">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.HAS_TLAST">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.HAS_TREADY">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.HAS_TSTRB">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.INSERT_VIP">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.LAYERED_METADATA">undef</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.PHASE">0.0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.TDATA_NUM_BYTES">16</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.TDEST_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.TID_WIDTH">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.TUSER_WIDTH">4</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.CFG_CTL_IF">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.CFG_FC_IF">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.CFG_MGMT_IF">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.CFG_STATUS_IF">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.C_DATA_WIDTH">128</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.ENABLE_JTAG_DBG">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.ERR_REPORTING_IF">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.EXT_CH_GT_DRP">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.EXT_PIPE_INTERFACE">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.EXT_STARTUP_PRIMITIVE">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.LINK_CAP_MAX_LINK_WIDTH">8</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.PCIE_ASYNC_EN">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.PCIE_EXT_CLK">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.PCIE_EXT_GT_COMMON">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.PIPE_SIM">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.PL_INTERFACE">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.RCV_MSG_IF">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.REDUCE_OOB_FREQ">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.SHARED_LOGIC_IN_CORE">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.TRANSCEIVER_CTRL_STATUS_PORTS">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.bar_0">FFF00000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.bar_1">00000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.bar_2">FFFF0004</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.bar_3">FFFFFFFF</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.bar_4">00000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.bar_5">00000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.bram_lat">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_aer_base_ptr">000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_aer_cap_ecrc_check_capable">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_aer_cap_ecrc_gen_capable">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_aer_cap_multiheader">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_aer_cap_nextptr">000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_aer_cap_on">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_aer_cap_optional_err_support">000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_aer_cap_permit_rooterr_update">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_buf_opt_bma">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_component_name">xdma_0_pcie2_ip</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_cpl_inf">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_cpl_infinite">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_cpl_timeout_disable_sup">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_cpl_timeout_range">0010</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_cpl_timeout_ranges_sup">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_d1_support">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_d2_support">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_de_emph">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_dev_cap2_ari_forwarding_supported">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_dev_cap2_atomicop32_completer_supported">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_dev_cap2_atomicop64_completer_supported">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_dev_cap2_atomicop_routing_supported">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_dev_cap2_cas128_completer_supported">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_dev_cap2_tph_completer_supported">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_dev_control_ext_tag_default">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_dev_port_type">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_dis_lane_reverse">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_disable_rx_poisoned_resp">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_disable_scrambling">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_disable_tx_aspm_l0s">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_dll_lnk_actv_cap">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_dsi_bool">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_dsn_base_ptr">100</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_dsn_cap_enabled">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_dsn_next_ptr">000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_enable_msg_route">00000000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_ep_l0s_accpt_lat">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_ep_l1_accpt_lat">7</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_ext_pci_cfg_space_addr">3FF</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_fc_cpld">205</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_fc_cplh">36</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_fc_npd">24</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_fc_nph">12</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_fc_pd">181</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_fc_ph">32</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_gen1">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_header_type">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_hw_auton_spd_disable">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_last_cfg_dw">10C</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_link_cap_aspm_optionality">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_ll_ack_timeout">0000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_ll_ack_timeout_enable">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_ll_ack_timeout_function">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_ll_replay_timeout">0000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_ll_replay_timeout_enable">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_ll_replay_timeout_func">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_msi">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_msi_64b_addr">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_msi_cap_on">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_msi_mult_msg_extn">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_msi_per_vctr_mask_cap">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_msix_cap_on">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_msix_next_ptr">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_msix_pba_bir">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_msix_pba_offset">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_msix_table_bir">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_msix_table_offset">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_msix_table_size">000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_pci_cfg_space_addr">3F</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_pcie_blk_locn">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_pcie_cap_next_ptr">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_pcie_cap_slot_implemented">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_pcie_dbg_ports">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_pcie_fast_config">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_perf_level_high">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_phantom_functions">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_pm_cap_next_ptr">48</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_pme_support">0F</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_base_ptr">000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_control_encodedbar0">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_control_encodedbar1">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_control_encodedbar2">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_control_encodedbar3">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_control_encodedbar4">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_control_encodedbar5">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_index0">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_index1">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_index2">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_index3">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_index4">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_index5">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_nextptr">000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_on">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_sup0">00001</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_sup1">00001</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_sup2">00001</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_sup3">00001</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_sup4">00001</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_cap_sup5">00001</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rbar_num">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rcb">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_recrc_check">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_recrc_check_trim">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_root_cap_crs">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rx_raddr_lat">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rx_ram_limit">3FF</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rx_rdata_lat">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_rx_write_lat">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_silicon_rev">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_slot_cap_attn_butn">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_slot_cap_attn_ind">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_slot_cap_elec_interlock">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_slot_cap_hotplug_cap">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_slot_cap_hotplug_surprise">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_slot_cap_mrl">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_slot_cap_no_cmd_comp_sup">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_slot_cap_physical_slot_num">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_slot_cap_pwr_ctrl">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_slot_cap_pwr_ind">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_slot_cap_pwr_limit_scale">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_slot_cap_pwr_limit_value">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_trgt_lnk_spd">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_trn_np_fc">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_tx_last_tlp">28</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_tx_raddr_lat">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_tx_rdata_lat">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_tx_write_lat">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_upconfig_capable">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_upstream_facing">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_ur_atomic">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_ur_inv_req">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_ur_prs_response">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_vc_base_ptr">000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_vc_cap_enabled">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_vc_cap_reject_snoop">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_vc_next_ptr">000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_vsec_base_ptr">000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_vsec_cap_enabled">FALSE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_vsec_next_ptr">000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.c_xlnx_ref_board">NONE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.cap_ver">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.cardbus_cis_ptr">00000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.class_code">070001</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.cmps">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.con_scl_fctr_d0_state">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.con_scl_fctr_d1_state">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.con_scl_fctr_d2_state">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.con_scl_fctr_d3_state">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.cost_table">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.d1_sup">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.d2_sup">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.dev_id">7028</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.dev_port_type">0000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.dis_scl_fctr_d0_state">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.dis_scl_fctr_d1_state">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.dis_scl_fctr_d2_state">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.dis_scl_fctr_d3_state">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.dsi">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.ep_l0s_accpt_lat">000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.ep_l1_accpt_lat">111</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.ext_tag_fld_sup">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.int_pin">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.intx">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.max_lnk_spd">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.max_lnk_wdt">001000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.mps">001</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.no_soft_rst">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.pci_exp_int_freq">3</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.pci_exp_ref_freq">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.phantm_func_sup">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.pme_sup">0F</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.pwr_con_d0_state">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.pwr_con_d1_state">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.pwr_con_d2_state">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.pwr_con_d3_state">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.pwr_dis_d0_state">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.pwr_dis_d1_state">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.pwr_dis_d2_state">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.pwr_dis_d3_state">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.rev_id">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.slot_clk">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.subsys_id">0007</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.subsys_ven_id">10EE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.ven_id">10EE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="MODELPARAM_VALUE.xrom_bar">00000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.ACK_NAK_Timeout_Func">Absolute</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.ACK_NAK_Timeout_Value">0000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_ACS_Violation">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_AtomicOp_Egress_Blocked">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_Completer_Abort">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_Completion_Timeout">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_Correctable_Internal_Error">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_ECRC_Check_Capable">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_ECRC_Error">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_ECRC_Gen_Capable">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_Enabled">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_Flow_Control_Protocol_Error">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_Header_Log_Overflow">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_MC_Blocked_TLP">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_Multiheader">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_Permit_Root_Error_Update">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_Receiver_Error">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_Receiver_Overflow">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_Surprise_Down">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_TLP_Prefix_Blocked">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AER_Uncorrectable_Internal_Error">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.ARI_Forwarding_Supported">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.ASPM_Optionality">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.ATOMICOP32_Completer_Supported">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.ATOMICOP64_Completer_Supported">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Acceptable_L0s_Latency">Maximum_of_64_ns</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Acceptable_L1_Latency">No_limit</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.AtomicOp_Routing_Supported">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.BAR0_Size_Vector">1M</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.BAR1_Size_Vector">1M</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.BAR2_Size_Vector">1M</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.BAR3_Size_Vector">1M</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.BAR4_Size_Vector">1M</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.BAR5_Size_Vector">1M</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.BAR_Index_Value0">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.BAR_Index_Value1">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.BAR_Index_Value2">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.BAR_Index_Value3">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.BAR_Index_Value4">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.BAR_Index_Value5">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar0_64bit">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar0_Enabled">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar0_Prefetchable">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar0_Scale">Megabytes</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar0_Size">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar0_Type">Memory</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar1_64bit">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar1_Enabled">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar1_Prefetchable">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar1_Scale">Kilobytes</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar1_Size">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar1_Type">N/A</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar2_64bit">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar2_Enabled">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar2_Prefetchable">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar2_Scale">Kilobytes</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar2_Size">64</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar2_Type">Memory</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar3_64bit">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar3_Enabled">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar3_Prefetchable">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar3_Scale">Kilobytes</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar3_Size">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar3_Type">N/A</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar4_64bit">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar4_Enabled">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar4_Prefetchable">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar4_Scale">Kilobytes</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar4_Size">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar4_Type">N/A</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar5_Enabled">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar5_Prefetchable">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar5_Scale">Kilobytes</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar5_Size">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Bar5_Type">N/A</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Base_Class_Menu">Simple_communication_controllers</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Buf_Opt_BMA">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.CAS128_Completer_Supported">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Cardbus_CIS_Pointer">00000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Class_Code_Base">07</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Class_Code_Interface">01</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Class_Code_Sub">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Component_Name">xdma_0_pcie2_ip</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Cost_Table">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Cpl_Finite">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Cpl_Timeout_Disable_Sup">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Cpl_Timeout_Range">Range_B</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D0_PME_Support">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D0_Power_Consumed">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D0_Power_Consumed_Factor">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D0_Power_Dissipated">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D0_Power_Dissipated_Factor">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D1_PME_Support">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D1_Power_Consumed">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D1_Power_Consumed_Factor">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D1_Power_Dissipated">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D1_Power_Dissipated_Factor">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D1_Support">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D2_PME_Support">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D2_Power_Consumed">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D2_Power_Consumed_Factor">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D2_Power_Dissipated">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D2_Power_Dissipated_Factor">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D2_Support">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D3_Power_Consumed">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D3_Power_Consumed_Factor">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D3_Power_Dissipated">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D3_Power_Dissipated_Factor">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D3cold_PME_Support">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.D3hot_PME_Support">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.DSN_Enabled">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.De_emph">-3.5</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Device_ID">7028</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Device_Port_Type">PCI_Express_Endpoint_device</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Device_Specific_Initialization">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Disable_Rx_Poisoned_Resp">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Disable_Tx_ASPM_L0s">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Dll_Link_Active_Cap">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Downstream_Link_Num">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.EXT_PCI_CFG_Space">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.EXT_PCI_CFG_Space_Addr">3FF</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.En_route_err_cor">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.En_route_err_ftl">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.En_route_err_nfl">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.En_route_inta">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.En_route_intb">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.En_route_intc">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.En_route_intd">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.En_route_pm_pme">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.En_route_pme_to">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.En_route_pme_to_ack">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.En_route_unlock">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Enable_ACK_NAK_Timer">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Enable_Lane_Reversal">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Enable_Replay_Timer">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Enable_Slot_Clock_Cfg">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Expansion_Rom_Enabled">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Expansion_Rom_Scale">Kilobytes</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Expansion_Rom_Size">2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Extended_Tag_Default">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Extended_Tag_Field">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Force_No_Scrambling">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Hw_Auton_Spd_Disable">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IO_Base_Limit_Registers">Disabled</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.IntX_Generation">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Interface_Width">128_bit</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Legacy_Interrupt">INTA</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Link_Speed">5.0_GT/s</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.MSI_64b">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.MSI_Enabled">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.MSI_Vec_Mask">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.MSIx_Enabled">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.MSIx_PBA_BIR">BAR_0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.MSIx_PBA_Offset">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.MSIx_Table_BIR">BAR_0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.MSIx_Table_Offset">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.MSIx_Table_Size">1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Max_Payload_Size">256_bytes</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Maximum_Link_Width">X8</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Multiple_Message_Capable">1_vector</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.No_Soft_Reset">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Optional_Error_Support">000000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.PCI_CFG_Space">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.PCI_CFG_Space_Addr">3F</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.PCIe_Blk_Locn">X0Y0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.PCIe_Cap_Slot_Implemented">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.PCIe_Debug_Ports">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Pcie_fast_config">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Perf_Level">High</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Phantom_Functions">No_function_number_bits_used</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Prefetchable_Memory_Base_Limit_Registers">Disabled</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RBAR_Enabled">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RBAR_Initial_Value0">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RBAR_Initial_Value1">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RBAR_Initial_Value2">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RBAR_Initial_Value3">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RBAR_Initial_Value4">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RBAR_Initial_Value5">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RBAR_Num">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RCB">64_byte</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RECRC_Check">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RECRC_Check_Trim">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.RESET_BOARD_INTERFACE">Custom</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Receive_NP_Request">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Ref_Clk_Freq">100_MHz</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Replay_Timeout_Func">Add</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Replay_Timeout_Value">0000</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Revision_ID">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Root_Cap_CRS">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Silicon_Rev">GES_and_Production</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Slot_Cap_Attn_Butn">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Slot_Cap_Attn_Ind">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Slot_Cap_Elec_Interlock">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Slot_Cap_HotPlug_Cap">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Slot_Cap_HotPlug_Surprise">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Slot_Cap_MRL">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Slot_Cap_No_Cmd_Comp_Sup">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Slot_Cap_Physical_Slot_Num">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Slot_Cap_Pwr_Ctrl">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Slot_Cap_Pwr_Ind">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Slot_Cap_Pwr_Limit_Scale">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Slot_Cap_Pwr_Limit_Value">0</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Sub_Class_Interface_Menu">16450_compatible_serial_controller</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Subsystem_ID">0007</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Subsystem_Vendor_ID">10EE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.TPH_Completer_Supported">00</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Trans_Buf_Pipeline">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Trgt_Link_Speed">4&apos;h2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.UR_Atomic">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.UR_INV_REQ">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.UR_PRS_RESPONSE">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.USE_BOARD_FLOW">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Upconfigure_Capable">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Use_Class_Code_Lookup_Assistant">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.User_Clk_Freq">250</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.VC_Cap_Enabled">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.VC_Cap_Reject_Snoop">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.VSEC_Enabled">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Vendor_ID">10EE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.Xlnx_Ref_Board">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.cfg_ctl_if">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.cfg_fc_if">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.cfg_mgmt_if">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.cfg_status_if">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.en_ext_ch_gt_drp">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.en_ext_clk">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.en_ext_gt_common">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.en_ext_pipe_interface">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.en_ext_startup">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.en_transceiver_status_ports">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.enable_jtag_dbg">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.err_reporting_if">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.mode_selection">Advanced</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.pipe_mode_sim">None</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.pipe_sim">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.pl_interface">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.rcv_msg_if">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.reduce_oob_freq">false</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PARAM_VALUE.shared_logic_in_core">true</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.ARCHITECTURE">kintex7</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.BASE_BOARD_PART"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.BOARD_CONNECTIONS"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.DEVICE">xc7k325t</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.PACKAGE">ffg900</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.PREFHDL">VERILOG</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SILICON_REVISION"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SIMULATOR_LANGUAGE">MIXED</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.SPEEDGRADE">-2</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.STATIC_POWER"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.TEMPERATURE_GRADE"/>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.USE_RDI_CUSTOMIZATION">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="PROJECT_PARAM.USE_RDI_GENERATION">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.IPCONTEXT">IP_Flow</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.IPREVISION">14</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.MANAGED">TRUE</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.OUTPUTDIR">.</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SELECTEDSIMMODEL"/>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SHAREDDIR">.</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SWVERSION">2021.1</spirit:configurableElementValue>
        <spirit:configurableElementValue spirit:referenceId="RUNTIME_PARAM.SYNTHESISFLOW">GLOBAL</spirit:configurableElementValue>
      </spirit:configurableElementValues>
      <spirit:vendorExtensions>
        <xilinx:componentInstanceExtensions>
          <xilinx:configElementInfos>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.HAS_TKEEP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.HAS_TLAST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.HAS_TREADY" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.HAS_TSTRB" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.TDATA_NUM_BYTES" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.TDEST_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.TID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_AXIS_RX.TUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.HAS_TKEEP" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.HAS_TLAST" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.HAS_TREADY" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.HAS_TSTRB" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.TDATA_NUM_BYTES" xilinx:valueSource="auto"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.TDEST_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.TID_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXIS_TX.TUSER_WIDTH" xilinx:valueSource="constant"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Bar0_64bit" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Bar0_Enabled" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Bar0_Scale" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Bar0_Size" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Bar1_Enabled" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Bar2_64bit" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Bar2_Enabled" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Bar2_Prefetchable" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Bar2_Scale" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Bar2_Size" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Base_Class_Menu" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Class_Code_Base" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Class_Code_Interface" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Class_Code_Sub" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Device_ID" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Device_Port_Type" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Enable_Lane_Reversal" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Enable_Slot_Clock_Cfg" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Extended_Tag_Field" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Interface_Width" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Legacy_Interrupt" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Link_Speed" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.MSI_Enabled" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.MSIx_Enabled" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Maximum_Link_Width" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Multiple_Message_Capable" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.PCIe_Blk_Locn" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.PCIe_Debug_Ports" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Pcie_fast_config" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Receive_NP_Request" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Ref_Clk_Freq" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Revision_ID" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Sub_Class_Interface_Menu" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Subsystem_ID" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Subsystem_Vendor_ID" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Use_Class_Code_Lookup_Assistant" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.User_Clk_Freq" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Vendor_ID" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.Xlnx_Ref_Board" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.en_ext_ch_gt_drp" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.en_ext_clk" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.en_ext_gt_common" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.en_ext_pipe_interface" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.en_ext_startup" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.en_transceiver_status_ports" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.mode_selection" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.pipe_mode_sim" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.pipe_sim" xilinx:valueSource="propagated"/>
            <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.shared_logic_in_core" xilinx:valueSource="propagated"/>
          </xilinx:configElementInfos>
          <xilinx:boundaryDescriptionInfo>
            <xilinx:boundaryDescription xilinx:boundaryDescriptionJSON="{&quot;ip_boundary&quot;:{&quot;ports&quot;:{&quot;cfg_aer_ecrc_check_en&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_aer_ecrc_gen_en&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_aer_interrupt_msgnum&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;4&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_aer_rooterr_corr_err_received&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_aer_rooterr_corr_err_reporting_en&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_aer_rooterr_fatal_err_received&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_aer_rooterr_fatal_err_reporting_en&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_aer_rooterr_non_fatal_err_received&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_aer_rooterr_non_fatal_err_reporting_en&quot;
:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_bridge_serr_en&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_bus_number&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_command&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_dcommand&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_dcommand2&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_device_number&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;4&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_ds_bus_number&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_ds_device_number&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;4&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_ds_function_number&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_
dsn&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;63&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_dstatus&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_err_acs&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_aer_headerlog&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;127&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_err_aer_headerlog_set&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_atomic_egress_blocked&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_cor&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_cpl_abort&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_cpl_rdy&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_cpl_timeout&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_ve
ctor&quot;:&quot;false&quot;}],&quot;cfg_err_cpl_unexpect&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_ecrc&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_internal_cor&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_internal_uncor&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_locked&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_malformed&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_mc_blocked&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_norecovery&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_poisoned&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_posted&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;phys
ical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_err_tlp_cpl_header&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;47&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_err_ur&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_function_number&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_interrupt&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_interrupt_assert&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_interrupt_di&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_interrupt_do&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_interrupt_mmenable&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_interrupt_msienable&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_interrupt_msixenable&quot;:[{&quot;direc
tion&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_interrupt_msixfm&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_interrupt_rdy&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_interrupt_stat&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_lcommand&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_lstatus&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_mgmt_byte_en&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;3&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_mgmt_di&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_mgmt_do&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_mgmt_dwaddr&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;9&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_mgmt_rd_en&quot;:[{&quot;direc
tion&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_mgmt_rd_wr_done&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_mgmt_wr_en&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_mgmt_wr_readonly&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_mgmt_wr_rw1c_as_rw&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_data&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_msg_received&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_assert_int_a&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_assert_int_b&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_assert_int_c&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physic
al_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_assert_int_d&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_deassert_int_a&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_deassert_int_b&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_deassert_int_c&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_deassert_int_d&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_err_cor&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_err_fatal&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_err_non_fatal&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_pm_as_nak&quot;:[{&quot;direction&quot;
:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_pm_pme&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_pme_to_ack&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_msg_received_setslotpowerlimit&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_pcie_link_state&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_pciecap_interrupt_msgnum&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;4&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_pm_force_state&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_pm_force_state_en&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_pm_halt_aspm_l0s&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_pm_halt_aspm_l1&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;
0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_pm_send_pme_to&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_pm_wake&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_pmcsr_pme_en&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_pmcsr_pme_status&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_pmcsr_powerstate&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_received_func_lvl_rst&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_root_control_pme_int_en&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_root_control_syserr_corr_err_en&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_root_control_syserr_fatal_err_en&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:
&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_root_control_syserr_non_fatal_err_en&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_slot_control_electromech_il_ctl_pulse&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_status&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;cfg_to_turnoff&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_trn_pending&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_turnoff_ok&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;cfg_vc_tcvc_map&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;6&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;fc_cpld&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;11&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;fc_cplh&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;fc_npd&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_l
eft&quot;:&quot;11&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;fc_nph&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;fc_pd&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;11&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;fc_ph&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;fc_sel&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;int_dclk_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;int_mmcm_lock_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;int_oobclk_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;int_pclk_out_slave&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;int_pclk_sel_slave&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;int_pipe_rxusrclk_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_righ
t&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;int_qplllock_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;int_qplloutclk_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;int_qplloutrefclk_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;int_rxoutclk_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;int_userclk1_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;int_userclk2_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;m_axis_rx_tdata&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;127&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;m_axis_rx_tkeep&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;m_axis_rx_tlast&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;m_axis_rx_tready&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_lef
t&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;m_axis_rx_tuser&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;21&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;m_axis_rx_tvalid&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;pci_exp_rxn&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;pci_exp_rxp&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;pci_exp_txn&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;pci_exp_txp&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;pl_directed_change_done&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;pl_directed_link_auton&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;pl_directed_link_change&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;pl_directed_link_speed&quot;:[{&quot;direction&quot;:&quot;i
n&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;pl_directed_link_width&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;pl_downstream_deemph_source&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;pl_initial_link_width&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;pl_lane_reversal_mode&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;pl_link_gen2_cap&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;pl_link_partner_gen2_supported&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;pl_link_upcfg_cap&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;pl_ltssm_state&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;5&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;pl_phy_lnk_up&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_ve
ctor&quot;:&quot;false&quot;}],&quot;pl_received_hot_rst&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;pl_rx_pm_state&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;pl_sel_lnk_rate&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;pl_sel_lnk_width&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;pl_transmit_hot_rst&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;pl_tx_pm_state&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;pl_upstream_prefer_deemph&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;rx_np_ok&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;rx_np_req&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axis_tx_tdata&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;127&quot;,&quot;physical_right&quot;:&quot;
0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axis_tx_tkeep&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axis_tx_tlast&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axis_tx_tready&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;s_axis_tx_tuser&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;3&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;s_axis_tx_tvalid&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;sys_clk&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;sys_rst_n&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;tx_buf_av&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;5&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;true&quot;}],&quot;tx_cfg_gnt&quot;:[{&quot;direction&quot;:&quot;in&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;tx_cfg_req&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;t
x_err_drop&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;user_app_rdy&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;user_clk_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;user_lnk_up&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}],&quot;user_reset_out&quot;:[{&quot;direction&quot;:&quot;out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;is_vector&quot;:&quot;false&quot;}]},&quot;interfaces&quot;:{&quot;CLK.sys_clk&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:clock:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:clock_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{&quot;ASSOCIATED_BUSIF&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;ASSOCIATED_RESET&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_stati
c_object&quot;:false}],&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;100000000&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;FREQ_TOLERANCE_HZ&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;CLK&quot;:[{&quot;physical_name&quot;:&quot;sys_clk&quot;,&quot;p
hysical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;CLK.user_clk_out&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:clock:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:clock_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{&quot;ASSOCIATED_BUSIF&quot;:[{&quot;value&quot;:&quot;m_axis_rx:s_axis_tx&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}],&quot;ASSOCIATED_RESET&quot;:[{&quot;value&quot;:&quot;user_reset_out&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}],&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;125000000&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred
&quot;:false,&quot;is_static_object&quot;:true}],&quot;FREQ_TOLERANCE_HZ&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;CLK&quot;:[{&quot;physical_name&quot;:&quot;user_clk_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;RST.sys_rst_n&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:reset:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:reset_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;lo
ng&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;POLARITY&quot;:[{&quot;value&quot;:&quot;ACTIVE_LOW&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}]},&quot;port_maps&quot;:{&quot;RST&quot;:[{&quot;physical_name&quot;:&quot;sys_rst_n&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;RST.user_reset_out&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:signal:reset:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:signal:reset_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;POLARITY&quot;:[{&quot;value&quot;:&quot;ACTIVE_HIGH&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;immediate&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;all&quot;,&quot;is_ips_inferred&quot;:false,&quot;is_static_object&quot;:true}]},&quot;port_maps&quot;:{&quot;RST&quot;:[{&quot;physical_name&quot;:&quot;user_res
et_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;m_axis_rx&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:axis:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:interface:axis_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;100000000&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;HAS_TKEEP&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;auto&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;HAS_TLAST&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;HAS_TREADY&quot;
:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;HAS_TSTRB&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;LAYERED_METADATA&quot;:[{&quot;value&quot;:&quot;undef&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;TDATA_NUM_BYTES&quot;:[{&quot;value&quot;:&quot;16&quot;,&quot;value_src&quot;:&quot;auto&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;r
esolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;TDEST_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;TID_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;TUSER_WIDTH&quot;:[{&quot;value&quot;:&quot;22&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;TDATA&quot;:[{&quot;physical_name&quot;:&quot;m_axis_rx_tdata&quot;,&quot;physical_left&quot;:&quot;127&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;127&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TKEEP&quot;:[{&quot;physical_name&quot;:&quot;m_axis_rx_tkeep&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;15&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TLAST&quot;:[{&quot;physical
_name&quot;:&quot;m_axis_rx_tlast&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TREADY&quot;:[{&quot;physical_name&quot;:&quot;m_axis_rx_tready&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TUSER&quot;:[{&quot;physical_name&quot;:&quot;m_axis_rx_tuser&quot;,&quot;physical_left&quot;:&quot;21&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;21&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TVALID&quot;:[{&quot;physical_name&quot;:&quot;m_axis_rx_tvalid&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;pcie2_cfg_control&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:pcie2_cfg_control:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:interface:pcie2_cfg_control_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{},&quot;port_maps&quot;:{&quot;ds_bus_number&quot;:[{&quot;physical_name&quot;:&quot;cfg_ds_bus_number&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;7&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;ds_device_number&quot;:[{&quot;physical_name&quot;:&quot;cfg_ds_device_number&quot;,&quot;phys
ical_left&quot;:&quot;4&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;4&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;ds_function_number&quot;:[{&quot;physical_name&quot;:&quot;cfg_ds_function_number&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;2&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;dsn&quot;:[{&quot;physical_name&quot;:&quot;cfg_dsn&quot;,&quot;physical_left&quot;:&quot;63&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;63&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pm_force_state&quot;:[{&quot;physical_name&quot;:&quot;cfg_pm_force_state&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;1&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pm_force_state_en&quot;:[{&quot;physical_name&quot;:&quot;cfg_pm_force_state_en&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pm_halt_aspm_l0s&quot;:[{&quot;physical_name&quot;:&quot;cfg_pm_halt_aspm_l0s&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pm_halt_aspm_l1&quot;:[{&quot;physical_name&quot;:&quot;cfg_pm_halt_aspm_l1&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_ri
ght&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pm_send_pme_to&quot;:[{&quot;physical_name&quot;:&quot;cfg_pm_send_pme_to&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pm_wake&quot;:[{&quot;physical_name&quot;:&quot;cfg_pm_wake&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;rx_np_ok&quot;:[{&quot;physical_name&quot;:&quot;rx_np_ok&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;rx_np_req&quot;:[{&quot;physical_name&quot;:&quot;rx_np_req&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;trn_pending&quot;:[{&quot;physical_name&quot;:&quot;cfg_trn_pending&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;turnoff_ok&quot;:[{&quot;physical_name&quot;:&quot;cfg_turnoff_ok&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;tx_cfg_gnt
&quot;:[{&quot;physical_name&quot;:&quot;tx_cfg_gnt&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;pcie2_cfg_err&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:pcie2_cfg_err:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:interface:pcie2_cfg_err_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{},&quot;port_maps&quot;:{&quot;acs&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_acs&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;aer_ecrc_check_en&quot;:[{&quot;physical_name&quot;:&quot;cfg_aer_ecrc_check_en&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;aer_ecrc_gen_en&quot;:[{&quot;physical_name&quot;:&quot;cfg_aer_ecrc_gen_en&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;aer_interrupt_msgnum&quot;:[{&quot;physical_name&quot;:&quot;cfg_aer_interrupt_msgnum&quot;,&quot;physical_left&quot;:&quot;4&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;4&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;atomic_egress_blocked&quot;:[{&quot;physical
_name&quot;:&quot;cfg_err_atomic_egress_blocked&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;cor&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_cor&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;cpl_abort&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_cpl_abort&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;cpl_rdy&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_cpl_rdy&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;cpl_timeout&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_cpl_timeout&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;cpl_unexpect&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_cpl_unexpect&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;ecrc&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_ecrc&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;
,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;err_aer_headerlog&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_aer_headerlog&quot;,&quot;physical_left&quot;:&quot;127&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;127&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;err_aer_headerlog_set&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_aer_headerlog_set&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;internal_cor&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_internal_cor&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;internal_uncor&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_internal_uncor&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;locked&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_locked&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;malformed&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_malformed&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;
0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;mc_blocked&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_mc_blocked&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;norecovery&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_norecovery&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;poisoned&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_poisoned&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;posted&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_posted&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;tlp_cpl_header&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_tlp_cpl_header&quot;,&quot;physical_left&quot;:&quot;47&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;47&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;ur&quot;:[{&quot;physical_name&quot;:&quot;cfg_err_ur&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;pcie2_cfg_interru
pt&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:pcie2_cfg_interrupt:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:interface:pcie2_cfg_interrupt_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{},&quot;port_maps&quot;:{&quot;assert&quot;:[{&quot;physical_name&quot;:&quot;cfg_interrupt_assert&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;interrupt&quot;:[{&quot;physical_name&quot;:&quot;cfg_interrupt&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;mmenable&quot;:[{&quot;physical_name&quot;:&quot;cfg_interrupt_mmenable&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;2&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;msienable&quot;:[{&quot;physical_name&quot;:&quot;cfg_interrupt_msienable&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;msixenable&quot;:[{&quot;physical_name&quot;:&quot;cfg_interrupt_msixenable&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;msixfm&quot;:[{&quot;physical_name&quot;:&quot;cfg_
interrupt_msixfm&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pciecap_interrupt_msgnum&quot;:[{&quot;physical_name&quot;:&quot;cfg_pciecap_interrupt_msgnum&quot;,&quot;physical_left&quot;:&quot;4&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;4&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;rdy&quot;:[{&quot;physical_name&quot;:&quot;cfg_interrupt_rdy&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;read_data&quot;:[{&quot;physical_name&quot;:&quot;cfg_interrupt_do&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;7&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;stat&quot;:[{&quot;physical_name&quot;:&quot;cfg_interrupt_stat&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;write_data&quot;:[{&quot;physical_name&quot;:&quot;cfg_interrupt_di&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;7&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;pcie2_cfg_msg_rcvd&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:pcie2_cfg_msg_rcvd:1.0&quot;,&quot;ab
straction_type&quot;:&quot;xilinx.com:interface:pcie2_cfg_msg_rcvd_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{},&quot;port_maps&quot;:{&quot;assert_int_a&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received_assert_int_a&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;assert_int_b&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received_assert_int_b&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;assert_int_c&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received_assert_int_c&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;assert_int_d&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received_assert_int_d&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;data&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_data&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;15&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;deassert_int_a&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received_dea
ssert_int_a&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;deassert_int_b&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received_deassert_int_b&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;deassert_int_c&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received_deassert_int_c&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;deassert_int_d&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received_deassert_int_d&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;err_cor&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received_err_cor&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;err_fatal&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received_err_fatal&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;err_non_fatal&quot;:[{&quot;physical_n
ame&quot;:&quot;cfg_msg_received_err_non_fatal&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pm_pme&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received_pm_pme&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pme_to_ack&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received_pme_to_ack&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;received&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;received_pm_as_nak&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received_pm_as_nak&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;received_setslotpowerlimit&quot;:[{&quot;physical_name&quot;:&quot;cfg_msg_received_setslotpowerlimit&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;pcie2_cf
g_status&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:pcie2_cfg_status:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:interface:pcie2_cfg_status_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{},&quot;port_maps&quot;:{&quot;aer_rooterr_corr_err_received&quot;:[{&quot;physical_name&quot;:&quot;cfg_aer_rooterr_corr_err_received&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;aer_rooterr_corr_err_reporting_en&quot;:[{&quot;physical_name&quot;:&quot;cfg_aer_rooterr_corr_err_reporting_en&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;aer_rooterr_fatal_err_received&quot;:[{&quot;physical_name&quot;:&quot;cfg_aer_rooterr_fatal_err_received&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;aer_rooterr_fatal_err_reporting_en&quot;:[{&quot;physical_name&quot;:&quot;cfg_aer_rooterr_fatal_err_reporting_en&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;aer_rooterr_non_fatal_err_received&quot;:
[{&quot;physical_name&quot;:&quot;cfg_aer_rooterr_non_fatal_err_received&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;aer_rooterr_non_fatal_err_reporting_en&quot;:[{&quot;physical_name&quot;:&quot;cfg_aer_rooterr_non_fatal_err_reporting_en&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;bridge_serr_en&quot;:[{&quot;physical_name&quot;:&quot;cfg_bridge_serr_en&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;bus_number&quot;:[{&quot;physical_name&quot;:&quot;cfg_bus_number&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;7&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;command&quot;:[{&quot;physical_name&quot;:&quot;cfg_command&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;15&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;dcommand&quot;:[{&quot;physical_name&quot;:&quot;cfg_dcommand&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;15&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;dcomm
and2&quot;:[{&quot;physical_name&quot;:&quot;cfg_dcommand2&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;15&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;device_number&quot;:[{&quot;physical_name&quot;:&quot;cfg_device_number&quot;,&quot;physical_left&quot;:&quot;4&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;4&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;dstatus&quot;:[{&quot;physical_name&quot;:&quot;cfg_dstatus&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;15&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;function_number&quot;:[{&quot;physical_name&quot;:&quot;cfg_function_number&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;2&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;lcommand&quot;:[{&quot;physical_name&quot;:&quot;cfg_lcommand&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;15&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;lstatus&quot;:[{&quot;physical_name&quot;:&quot;cfg_lstatus&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;15&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pcie_link_state&quot;:[{&quot;physical_name&quot;:&quot;cfg_pcie_link_state&quot;,&quot;physical_left&quot;:&quot;
2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;2&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pmcsr_pme_en&quot;:[{&quot;physical_name&quot;:&quot;cfg_pmcsr_pme_en&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pmcsr_pme_status&quot;:[{&quot;physical_name&quot;:&quot;cfg_pmcsr_pme_status&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pmcsr_powerstate&quot;:[{&quot;physical_name&quot;:&quot;cfg_pmcsr_powerstate&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;1&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;received_func_lvl_rst&quot;:[{&quot;physical_name&quot;:&quot;cfg_received_func_lvl_rst&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;root_control_pme_int_en&quot;:[{&quot;physical_name&quot;:&quot;cfg_root_control_pme_int_en&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;root_control_syserr_corr_err_en&quot;:[{&quot;physical_name&quot;:&quot;cfg_root_co
ntrol_syserr_corr_err_en&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;root_control_syserr_fatal_err_en&quot;:[{&quot;physical_name&quot;:&quot;cfg_root_control_syserr_fatal_err_en&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;root_control_syserr_non_fatal_err_en&quot;:[{&quot;physical_name&quot;:&quot;cfg_root_control_syserr_non_fatal_err_en&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;slot_control_electromech_il_ctl_pulse&quot;:[{&quot;physical_name&quot;:&quot;cfg_slot_control_electromech_il_ctl_pulse&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;status&quot;:[{&quot;physical_name&quot;:&quot;cfg_status&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;15&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;turnoff&quot;:[{&quot;physical_name&quot;:&quot;cfg_to_turnoff&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;l
ogical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;tx_buf_av&quot;:[{&quot;physical_name&quot;:&quot;tx_buf_av&quot;,&quot;physical_left&quot;:&quot;5&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;5&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;tx_cfg_req&quot;:[{&quot;physical_name&quot;:&quot;tx_cfg_req&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;tx_err_drop&quot;:[{&quot;physical_name&quot;:&quot;tx_err_drop&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;vc_tcvc_map&quot;:[{&quot;physical_name&quot;:&quot;cfg_vc_tcvc_map&quot;,&quot;physical_left&quot;:&quot;6&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;6&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;pcie2_pl&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:pcie2_pl:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:interface:pcie2_pl_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{},&quot;port_maps&quot;:{&quot;directed_change_done&quot;:[{&quot;physical_name&quot;:&quot;pl_directed_change_done&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;directed_link_a
uton&quot;:[{&quot;physical_name&quot;:&quot;pl_directed_link_auton&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;directed_link_change&quot;:[{&quot;physical_name&quot;:&quot;pl_directed_link_change&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;1&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;directed_link_speed&quot;:[{&quot;physical_name&quot;:&quot;pl_directed_link_speed&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;directed_link_width&quot;:[{&quot;physical_name&quot;:&quot;pl_directed_link_width&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;1&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;downstream_deemph_source&quot;:[{&quot;physical_name&quot;:&quot;pl_downstream_deemph_source&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;initial_link_width&quot;:[{&quot;physical_name&quot;:&quot;pl_initial_link_width&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;2&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port
_maps_used&quot;:&quot;none&quot;}],&quot;lane_reversal_mode&quot;:[{&quot;physical_name&quot;:&quot;pl_lane_reversal_mode&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;1&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;link_gen2_cap&quot;:[{&quot;physical_name&quot;:&quot;pl_link_gen2_cap&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;link_partner_gen2_supported&quot;:[{&quot;physical_name&quot;:&quot;pl_link_partner_gen2_supported&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;link_upcfg_cap&quot;:[{&quot;physical_name&quot;:&quot;pl_link_upcfg_cap&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;ltssm_state&quot;:[{&quot;physical_name&quot;:&quot;pl_ltssm_state&quot;,&quot;physical_left&quot;:&quot;5&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;5&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;phy_lnk_up&quot;:[{&quot;physical_name&quot;:&quot;pl_phy_lnk_up&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;no
ne&quot;}],&quot;received_hot_rst&quot;:[{&quot;physical_name&quot;:&quot;pl_received_hot_rst&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;rx_pm_state&quot;:[{&quot;physical_name&quot;:&quot;pl_rx_pm_state&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;1&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;sel_lnk_rate&quot;:[{&quot;physical_name&quot;:&quot;pl_sel_lnk_rate&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;sel_lnk_width&quot;:[{&quot;physical_name&quot;:&quot;pl_sel_lnk_width&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;1&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;transmit_hot_rst&quot;:[{&quot;physical_name&quot;:&quot;pl_transmit_hot_rst&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;tx_pm_state&quot;:[{&quot;physical_name&quot;:&quot;pl_tx_pm_state&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;2&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;upstream_prefer_deemph&quot;:[{&quot;physical_
name&quot;:&quot;pl_upstream_prefer_deemph&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;pcie_7x_mgt&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:pcie_7x_mgt:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:interface:pcie_7x_mgt_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{},&quot;port_maps&quot;:{&quot;rxn&quot;:[{&quot;physical_name&quot;:&quot;pci_exp_rxn&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;7&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;rxp&quot;:[{&quot;physical_name&quot;:&quot;pci_exp_rxp&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;7&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;txn&quot;:[{&quot;physical_name&quot;:&quot;pci_exp_txn&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;7&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;txp&quot;:[{&quot;physical_name&quot;:&quot;pci_exp_txp&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;7&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;pcie_cfg_fc&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:pcie_cfg_fc:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:interface:pcie_cfg_
fc_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{},&quot;port_maps&quot;:{&quot;CPLD&quot;:[{&quot;physical_name&quot;:&quot;fc_cpld&quot;,&quot;physical_left&quot;:&quot;11&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;11&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;CPLH&quot;:[{&quot;physical_name&quot;:&quot;fc_cplh&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;7&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;NPD&quot;:[{&quot;physical_name&quot;:&quot;fc_npd&quot;,&quot;physical_left&quot;:&quot;11&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;11&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;NPH&quot;:[{&quot;physical_name&quot;:&quot;fc_nph&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;7&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;PD&quot;:[{&quot;physical_name&quot;:&quot;fc_pd&quot;,&quot;physical_left&quot;:&quot;11&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;11&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;PH&quot;:[{&quot;physical_name&quot;:&quot;fc_ph&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;7&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;SEL&quot;:[{&quot;physical_name&quot;:&quot;fc_sel&quot;,&quot;physical_left&quot;:&quot;2&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;2&quot;,&quot;logical_
right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;pcie_cfg_mgmt&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:pcie_cfg_mgmt:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:interface:pcie_cfg_mgmt_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{},&quot;port_maps&quot;:{&quot;ADDR&quot;:[{&quot;physical_name&quot;:&quot;cfg_mgmt_dwaddr&quot;,&quot;physical_left&quot;:&quot;9&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;9&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;BYTE_EN&quot;:[{&quot;physical_name&quot;:&quot;cfg_mgmt_byte_en&quot;,&quot;physical_left&quot;:&quot;3&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;3&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;READONLY&quot;:[{&quot;physical_name&quot;:&quot;cfg_mgmt_wr_readonly&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;READ_DATA&quot;:[{&quot;physical_name&quot;:&quot;cfg_mgmt_do&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;31&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;READ_EN&quot;:[{&quot;physical_name&quot;:&quot;cfg_mgmt_rd_en&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;READ_WRITE_DONE&quot;:[{&quot;phy
sical_name&quot;:&quot;cfg_mgmt_rd_wr_done&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TYPE1_CFG_REG_ACCESS&quot;:[{&quot;physical_name&quot;:&quot;cfg_mgmt_wr_rw1c_as_rw&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;WRITE_DATA&quot;:[{&quot;physical_name&quot;:&quot;cfg_mgmt_di&quot;,&quot;physical_left&quot;:&quot;31&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;31&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;WRITE_EN&quot;:[{&quot;physical_name&quot;:&quot;cfg_mgmt_wr_en&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;pcie_sharedlogic_int_clk&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:pcie_sharedlogic_int_clk:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:interface:pcie_sharedlogic_int_clk_rtl:1.0&quot;,&quot;mode&quot;:&quot;master&quot;,&quot;parameters&quot;:{},&quot;port_maps&quot;:{&quot;dclk&quot;:[{&quot;physical_name&quot;:&quot;int_dclk_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;mmcm_lock&quot;:[{&quot;physical_nam
e&quot;:&quot;int_mmcm_lock_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;oobclk&quot;:[{&quot;physical_name&quot;:&quot;int_oobclk_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pclk_sel_slave&quot;:[{&quot;physical_name&quot;:&quot;int_pclk_sel_slave&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;7&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pclk_slave&quot;:[{&quot;physical_name&quot;:&quot;int_pclk_out_slave&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;pipe_rxusrclk&quot;:[{&quot;physical_name&quot;:&quot;int_pipe_rxusrclk_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;qplllock&quot;:[{&quot;physical_name&quot;:&quot;int_qplllock_out&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;1&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;qplloutclk&quot;:[{&quot;physical_name&quot;:&quot;int_qplloutclk_out&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_
right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;1&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;qplloutrefclk&quot;:[{&quot;physical_name&quot;:&quot;int_qplloutrefclk_out&quot;,&quot;physical_left&quot;:&quot;1&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;1&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;rxoutclk&quot;:[{&quot;physical_name&quot;:&quot;int_rxoutclk_out&quot;,&quot;physical_left&quot;:&quot;7&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;7&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;usrclk1&quot;:[{&quot;physical_name&quot;:&quot;int_userclk1_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;usrclk2&quot;:[{&quot;physical_name&quot;:&quot;int_userclk2_out&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}},&quot;s_axis_tx&quot;:{&quot;vlnv&quot;:&quot;xilinx.com:interface:axis:1.0&quot;,&quot;abstraction_type&quot;:&quot;xilinx.com:interface:axis_rtl:1.0&quot;,&quot;mode&quot;:&quot;slave&quot;,&quot;parameters&quot;:{&quot;CLK_DOMAIN&quot;:[{&quot;value&quot;:&quot;&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_
static_object&quot;:false}],&quot;FREQ_HZ&quot;:[{&quot;value&quot;:&quot;100000000&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;HAS_TKEEP&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;auto&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;HAS_TLAST&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;HAS_TREADY&quot;:[{&quot;value&quot;:&quot;1&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;HAS_TSTRB&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;INSERT_VIP&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_per
mission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;user&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;simulation.rtl&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;LAYERED_METADATA&quot;:[{&quot;value&quot;:&quot;undef&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;string&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;PHASE&quot;:[{&quot;value&quot;:&quot;0.0&quot;,&quot;value_src&quot;:&quot;default&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;float&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;TDATA_NUM_BYTES&quot;:[{&quot;value&quot;:&quot;16&quot;,&quot;value_src&quot;:&quot;auto&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;TDEST_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;TID_WIDTH&quot;:[{&quot;value&quot;:&quot;0&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usag
e&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}],&quot;TUSER_WIDTH&quot;:[{&quot;value&quot;:&quot;4&quot;,&quot;value_src&quot;:&quot;constant&quot;,&quot;value_permission&quot;:&quot;user&quot;,&quot;resolve_type&quot;:&quot;generated&quot;,&quot;format&quot;:&quot;long&quot;,&quot;usage&quot;:&quot;none&quot;,&quot;is_ips_inferred&quot;:true,&quot;is_static_object&quot;:false}]},&quot;port_maps&quot;:{&quot;TDATA&quot;:[{&quot;physical_name&quot;:&quot;s_axis_tx_tdata&quot;,&quot;physical_left&quot;:&quot;127&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;127&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TKEEP&quot;:[{&quot;physical_name&quot;:&quot;s_axis_tx_tkeep&quot;,&quot;physical_left&quot;:&quot;15&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;15&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TLAST&quot;:[{&quot;physical_name&quot;:&quot;s_axis_tx_tlast&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TREADY&quot;:[{&quot;physical_name&quot;:&quot;s_axis_tx_tready&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}],&quot;TUSER&quot;:[{&quot;physical_name&quot;:&quot;s_axis_tx_tuser&quot;,&quot;physical_left&quot;:&quot;3&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;3&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_ma
ps_used&quot;:&quot;none&quot;}],&quot;TVALID&quot;:[{&quot;physical_name&quot;:&quot;s_axis_tx_tvalid&quot;,&quot;physical_left&quot;:&quot;0&quot;,&quot;physical_right&quot;:&quot;0&quot;,&quot;logical_left&quot;:&quot;0&quot;,&quot;logical_right&quot;:&quot;0&quot;,&quot;port_maps_used&quot;:&quot;none&quot;}]}}}}}"/>
          </xilinx:boundaryDescriptionInfo>
        </xilinx:componentInstanceExtensions>
      </spirit:vendorExtensions>
    </spirit:componentInstance>
  </spirit:componentInstances>
</spirit:design>
