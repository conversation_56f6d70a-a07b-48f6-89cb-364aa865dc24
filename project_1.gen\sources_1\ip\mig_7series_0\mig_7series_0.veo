//*****************************************************************************


// (c) Copyright 2009 - 2010 Xilinx, Inc. All rights reserved.


//


// This file contains confidential and proprietary information


// of Xilinx, Inc. and is protected under U.S. and


// international copyright and other intellectual property


// laws.


//


// DISCLAIMER


// This disclaimer is not a license and does not grant any


// rights to the materials distributed herewith. Except as


// otherwise provided in a valid license issued to you by


// Xilinx, and to the maximum extent permitted by applicable


// law: (1) THESE MATERIALS ARE MADE AVAILABLE "AS IS" AND


// WITH ALL FAULTS, AND XILINX HEREBY DISCLAIMS ALL WARRANTIES


// AND CONDITIONS, EXPRESS, IMPLIED, OR STATUTORY, INCLUDING


// BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, NON-


// INFRINGEMENT, OR FITNESS FOR ANY PARTICULAR PURPOSE; and


// (2) Xilinx shall not be liable (whether in contract or tort,


// including negligence, or under any other theory of


// liability) for any loss or damage of any kind or nature


// related to, arising under or in connection with these


// materials, including for any direct, or any indirect,


// special, incidental, or consequential loss or damage


// (including loss of data, profits, goodwill, or any type of


// loss or damage suffered as a result of any action brought


// by a third party) even if such damage or loss was


// reasonably foreseeable or Xilinx had been advised of the


// possibility of the same.


//


// CRITICAL APPLICATIONS


// Xilinx products are not designed or intended to be fail-


// safe, or for use in any application requiring fail-safe


// performance, such as life-support or safety devices or


// systems, Class III medical devices, nuclear facilities,


// applications related to the deployment of airbags, or any


// other applications that could lead to death, personal


// injury, or severe property or environmental damage


// (individually and collectively, "Critical


// Applications"). Customer assumes the sole risk and


// liability of any use of Xilinx products in Critical


// Applications, subject only to applicable laws and


// regulations governing limitations on product liability.


//


// THIS COPYRIGHT NOTICE AND DISCLAIMER MUST BE RETAINED AS


// PART OF THIS FILE AT ALL TIMES.


//


//*****************************************************************************


//   ____  ____


//  /   /\/   /


// /___/  \  /   Vendor             : Xilinx


// \   \   \/    Version            : 4.2


//  \   \        Application        : MIG


//  /   /        Filename           : mig_7series_0.veo


// /___/   /\    Date Last Modified : $Date: 2011/06/02 08:34:47 $


// \   \  /  \   Date Created       : Tue Sept 21 2010


//  \___\/\___\


//


// Device           : 7 Series


// Design Name      : DDR3 SDRAM


// Purpose          : Template file containing code that can be used as a model


//                    for instantiating a CORE Generator module in a HDL design.


// Revision History :


//*****************************************************************************





// The following must be inserted into your Verilog file for this


// core to be instantiated. Change the instance name and port connections


// (in parentheses) to your own signal names.





//----------- Begin Cut here for INSTANTIATION Template ---// INST_TAG





  mig_7series_0 u_mig_7series_0 (





    // Memory interface ports


    .ddr3_addr                      (ddr3_addr),  // output [14:0]		ddr3_addr


    .ddr3_ba                        (ddr3_ba),  // output [2:0]		ddr3_ba


    .ddr3_cas_n                     (ddr3_cas_n),  // output			ddr3_cas_n


    .ddr3_ck_n                      (ddr3_ck_n),  // output [0:0]		ddr3_ck_n


    .ddr3_ck_p                      (ddr3_ck_p),  // output [0:0]		ddr3_ck_p


    .ddr3_cke                       (ddr3_cke),  // output [0:0]		ddr3_cke


    .ddr3_ras_n                     (ddr3_ras_n),  // output			ddr3_ras_n


    .ddr3_reset_n                   (ddr3_reset_n),  // output			ddr3_reset_n


    .ddr3_we_n                      (ddr3_we_n),  // output			ddr3_we_n


    .ddr3_dq                        (ddr3_dq),  // inout [71:0]		ddr3_dq


    .ddr3_dqs_n                     (ddr3_dqs_n),  // inout [8:0]		ddr3_dqs_n


    .ddr3_dqs_p                     (ddr3_dqs_p),  // inout [8:0]		ddr3_dqs_p


    .init_calib_complete            (init_calib_complete),  // output			init_calib_complete


      


	.ddr3_cs_n                      (ddr3_cs_n),  // output [0:0]		ddr3_cs_n


    .ddr3_odt                       (ddr3_odt),  // output [0:0]		ddr3_odt


    // Application interface ports


    .app_addr                       (app_addr),  // input [28:0]		app_addr


    .app_cmd                        (app_cmd),  // input [2:0]		app_cmd


    .app_en                         (app_en),  // input				app_en


    .app_wdf_data                   (app_wdf_data),  // input [511:0]		app_wdf_data


    .app_wdf_end                    (app_wdf_end),  // input				app_wdf_end


    .app_wdf_wren                   (app_wdf_wren),  // input				app_wdf_wren


    .app_rd_data                    (app_rd_data),  // output [511:0]		app_rd_data


    .app_rd_data_end                (app_rd_data_end),  // output			app_rd_data_end


    .app_rd_data_valid              (app_rd_data_valid),  // output			app_rd_data_valid


    .app_rdy                        (app_rdy),  // output			app_rdy


    .app_wdf_rdy                    (app_wdf_rdy),  // output			app_wdf_rdy


    .app_sr_req                     (app_sr_req),  // input			app_sr_req


    .app_ref_req                    (app_ref_req),  // input			app_ref_req


    .app_zq_req                     (app_zq_req),  // input			app_zq_req


    .app_sr_active                  (app_sr_active),  // output			app_sr_active


    .app_ref_ack                    (app_ref_ack),  // output			app_ref_ack


    .app_zq_ack                     (app_zq_ack),  // output			app_zq_ack


    .ui_clk                         (ui_clk),  // output			ui_clk


    .ui_clk_sync_rst                (ui_clk_sync_rst),  // output			ui_clk_sync_rst


    .app_correct_en_i               (app_correct_en_i),  // input			app_correct_en_i


    .app_ecc_multiple_err           (app_ecc_multiple_err),  // output [7:0]			app_ecc_multiple_err


    .app_wdf_mask                   (app_wdf_mask),  // input [63:0]		app_wdf_mask


    // System Clock Ports


    .sys_clk_i                       (sys_clk_i),


    .sys_rst                        (sys_rst) // input sys_rst


    );





// INST_TAG_END ------ End INSTANTIATION Template ---------





// You must compile the wrapper file mig_7series_0.v when simulating


// the core, mig_7series_0. When compiling the wrapper file, be sure to


// reference the XilinxCoreLib Verilog simulation library. For detailed


// instructions, please refer to the "CORE Generator Help".







