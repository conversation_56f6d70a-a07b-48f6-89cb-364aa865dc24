#=============================================================================
# TCL Script: run_simulation.tcl - DMA IP Simulation Setup
# TCL脚本: run_simulation.tcl - DMA IP仿真设置
#=============================================================================
# DESCRIPTION / 功能描述:
# Automated simulation setup script for the DMA IP top module
# DMA IP顶层模块的自动化仿真设置脚本
#
# This script configures and runs simulation for:
# 该脚本配置并运行以下仿真：
# - Complete testbench with all 5 FIFO channels
#   包含所有5个FIFO通道的完整测试平台
# - Automated compilation and simulation launch
#   自动化编译和仿真启动
# - Waveform configuration and viewing
#   波形配置和查看
#
# USAGE / 使用方法:
# From Vivado TCL console: source run_simulation.tcl
# 在Vivado TCL控制台中：source run_simulation.tcl
#=============================================================================

# Script configuration / 脚本配置
set script_name "run_simulation.tcl"
set project_name "dma_ip_simulation"
set top_module "tb_dma_ip_top_generic"

puts "\n=========================================="
puts "DMA IP Simulation Setup Script"
puts "DMA IP仿真设置脚本"
puts "==========================================\n"

# Set simulation directory / 设置仿真目录
set sim_dir "./sim_results"
file mkdir $sim_dir
cd $sim_dir

puts "INFO: Setting up simulation in directory: [pwd]"

#=============================================================================
# Step 1: Create Simulation Project / 步骤1：创建仿真项目
#=============================================================================

puts "INFO: Creating simulation project..."
puts "信息：创建仿真项目..."

# Create new project / 创建新项目
if {[file exists $project_name]} {
    puts "WARNING: Project directory exists, cleaning up..."
    puts "警告：项目目录存在，正在清理..."
    file delete -force $project_name
}

create_project $project_name $project_name -part xc7k325tffg900-2 -force

# Set project properties / 设置项目属性
set_property target_language Verilog [current_project]
set_property simulator_language Mixed [current_project]
set_property default_lib work [current_project]

#=============================================================================
# Step 2: Add Source Files / 步骤2：添加源文件
#=============================================================================

puts "INFO: Adding source files to project..."
puts "信息：向项目添加源文件..."

# Add design source files / 添加设计源文件
set source_files [list \
    "../dma_ip_top_generic.v" \
    "../xdma_app_generic.v" \
    "../myip_v1_0_S00_AXI_generic.v" \
    "../example_dma_system.v" \
]

foreach src_file $source_files {
    if {[file exists $src_file]} {
        add_files -norecurse $src_file
        puts "  Added: $src_file"
    } else {
        puts "WARNING: Source file not found: $src_file"
        puts "警告：找不到源文件: $src_file"
    }
}

# Add testbench files / 添加测试平台文件
set tb_files [list \
    "../tb_dma_ip_top_generic.v" \
]

foreach tb_file $tb_files {
    if {[file exists $tb_file]} {
        add_files -fileset sim_1 -norecurse $tb_file
        puts "  Added testbench: $tb_file"
    } else {
        puts "WARNING: Testbench file not found: $tb_file"
        puts "警告：找不到测试平台文件: $tb_file"
    }
}

# Set top module for simulation / 设置仿真顶层模块
set_property top $top_module [get_filesets sim_1]
set_property top_lib xil_defaultlib [get_filesets sim_1]

puts "INFO: Source files added successfully"
puts "信息：源文件添加成功"

#=============================================================================
# Step 3: Configure IP Cores / 步骤3：配置IP核心
#=============================================================================

puts "INFO: Configuring IP cores..."
puts "信息：配置IP核心..."

# Note: In a real project, you would need to add XDMA IP and FIFO IP cores
# 注意：在真实项目中，您需要添加XDMA IP和FIFO IP核心
# For this simulation, we assume the IP cores are already available
# 对于此仿真，我们假设IP核心已经可用

# Example IP configuration (commented out for basic simulation)
# 示例IP配置（为基本仿真注释掉）
# create_ip -name fifo_generator -vendor xilinx.com -library ip -version 13.2 -module_name fifo_128_128
# set_property -dict [list CONFIG.Fifo_Implementation {Common_Clock_Block_RAM}] [get_ips fifo_128_128]

puts "INFO: IP configuration completed"
puts "信息：IP配置完成"

#=============================================================================
# Step 4: Set Simulation Properties / 步骤4：设置仿真属性
#=============================================================================

puts "INFO: Configuring simulation properties..."
puts "信息：配置仿真属性..."

# Set simulation properties / 设置仿真属性
set_property -name {xsim.simulate.runtime} -value {50ms} -objects [get_filesets sim_1]
set_property -name {xsim.simulate.log_all_signals} -value {true} -objects [get_filesets sim_1]
set_property -name {xsim.simulate.wdb} -value {tb_dma_ip_top_generic_behav.wdb} -objects [get_filesets sim_1]

# Set simulator properties / 设置仿真器属性
set_property target_simulator XSim [current_project]
set_property compxlib.xsim_compiled_library_dir {} [current_project]

# Configure wave database / 配置波形数据库
set_property -name {xsim.simulate.saif} -value {} -objects [get_filesets sim_1]
set_property -name {xsim.simulate.tcl.pre} -value {} -objects [get_filesets sim_1]
set_property -name {xsim.simulate.tcl.post} -value {} -objects [get_filesets sim_1]

puts "INFO: Simulation properties configured"
puts "信息：仿真属性配置完成"

#=============================================================================
# Step 5: Create Simulation Script / 步骤5：创建仿真脚本
#=============================================================================

puts "INFO: Creating simulation run script..."
puts "信息：创建仿真运行脚本..."

# Create simulation TCL commands / 创建仿真TCL命令
set sim_script_content {
# DMA IP Simulation Commands
# DMA IP仿真命令

# Add all signals to wave window
add_wave_divider "Top Level Signals"
add_wave /tb_dma_ip_top_generic/*

add_wave_divider "Clock and Reset"
add_wave /tb_dma_ip_top_generic/sys_clk_p
add_wave /tb_dma_ip_top_generic/sys_clk_n
add_wave /tb_dma_ip_top_generic/sys_rst_n
add_wave /tb_dma_ip_top_generic/user_clk
add_wave /tb_dma_ip_top_generic/user_reset_n

add_wave_divider "Test Control"
add_wave /tb_dma_ip_top_generic/test_phase
add_wave /tb_dma_ip_top_generic/test_active
add_wave /tb_dma_ip_top_generic/simulation_time

add_wave_divider "Upstream Channel 0 (GTX)"
add_wave /tb_dma_ip_top_generic/user_data_in
add_wave /tb_dma_ip_top_generic/user_wr
add_wave /tb_dma_ip_top_generic/up_fifo_full
add_wave /tb_dma_ip_top_generic/gtx_packet_count

add_wave_divider "Upstream Channel 1 (DDR)"
add_wave /tb_dma_ip_top_generic/user_data_in1
add_wave /tb_dma_ip_top_generic/user_wr1
add_wave /tb_dma_ip_top_generic/up_fifo_full1
add_wave /tb_dma_ip_top_generic/ddr_packet_count

add_wave_divider "Downstream Channel 0 (Direct)"
add_wave /tb_dma_ip_top_generic/dn_fifo_q
add_wave /tb_dma_ip_top_generic/dn_fifo_rd
add_wave /tb_dma_ip_top_generic/dn_fifo_emp
add_wave /tb_dma_ip_top_generic/direct_read_count

add_wave_divider "Downstream Channel 1 (TLK2711)"
add_wave /tb_dma_ip_top_generic/dn_fifo1_q
add_wave /tb_dma_ip_top_generic/dn_fifo1_rd
add_wave /tb_dma_ip_top_generic/dn_fifo1_emp
add_wave /tb_dma_ip_top_generic/tlk_read_count

add_wave_divider "Downstream Channel 2 (UDP)"
add_wave /tb_dma_ip_top_generic/dn_fifo2_q
add_wave /tb_dma_ip_top_generic/dn_fifo2_rd
add_wave /tb_dma_ip_top_generic/dn_fifo2_emp
add_wave /tb_dma_ip_top_generic/udp_read_count

add_wave_divider "FIFO Status"
add_wave /tb_dma_ip_top_generic/dn_fifo0_full
add_wave /tb_dma_ip_top_generic/dn_fifo1_full
add_wave /tb_dma_ip_top_generic/dn_fifo2_full

# Configure wave window
configure_wave -namecolwidth 300
configure_wave -valuecolwidth 150
configure_wave -timelineunits ns
update_compile_order -fileset sources_1

# Run simulation
puts "Starting simulation..."
run 50ms

# Zoom to fit and configure display
wave zoom full
puts "Simulation completed. Check waveforms in the wave window."
}

# Write simulation script to file / 将仿真脚本写入文件
set sim_script_file "sim_commands.tcl"
set fp [open $sim_script_file w]
puts $fp $sim_script_content
close $fp

puts "INFO: Simulation script created: $sim_script_file"
puts "信息：仿真脚本已创建: $sim_script_file"

#=============================================================================
# Step 6: Launch Simulation / 步骤6：启动仿真
#=============================================================================

puts "INFO: Launching simulation..."
puts "信息：启动仿真..."

# Update compile order / 更新编译顺序
update_compile_order -fileset sources_1
update_compile_order -fileset sim_1

# Launch simulation / 启动仿真
launch_simulation

# Execute simulation commands / 执行仿真命令
if {[file exists $sim_script_file]} {
    source $sim_script_file
} else {
    puts "WARNING: Simulation command script not found"
    puts "警告：找不到仿真命令脚本"
    
    # Basic simulation commands / 基本仿真命令
    add_wave /*
    run 50ms
    wave zoom full
}

puts "\n=========================================="
puts "Simulation Setup Complete!"
puts "仿真设置完成！"
puts "=========================================="
puts "Simulation is now running. Check the waveform window for results."
puts "仿真正在运行。检查波形窗口查看结果。"
puts "Use 'run' command to continue simulation if needed."
puts "如需要，使用'run'命令继续仿真。"
puts "==========================================\n"

#=============================================================================
# Helper Procedures / 辅助程序
#=============================================================================

# Procedure to restart simulation / 重启仿真的程序
proc restart_sim {} {
    puts "Restarting simulation..."
    puts "重启仿真..."
    restart
    run 50ms
    wave zoom full
    puts "Simulation restarted."
    puts "仿真已重启。"
}

# Procedure to add more signals / 添加更多信号的程序
proc add_internal_signals {} {
    puts "Adding internal DUT signals..."
    puts "添加内部DUT信号..."
    
    add_wave_divider "DUT Internal Signals"
    add_wave /tb_dma_ip_top_generic/dut/*
    
    add_wave_divider "XDMA App Internal"
    add_wave /tb_dma_ip_top_generic/dut/xdma_app_i/*
    
    wave zoom full
    puts "Internal signals added."
    puts "内部信号已添加。"
}

# Print usage information / 打印使用信息
puts "Available helper commands:"
puts "可用的辅助命令："
puts "  restart_sim           - Restart the simulation"
puts "  restart_sim           - 重启仿真"
puts "  add_internal_signals  - Add DUT internal signals to wave"
puts "  add_internal_signals  - 向波形添加DUT内部信号"
puts "" 