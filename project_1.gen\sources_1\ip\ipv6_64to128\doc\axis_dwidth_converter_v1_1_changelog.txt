2021.1:
 * Version 1.1 (Rev. 23)
 * General: Removed tlm models
 * Revision change in one or more subcores

2020.3:
 * Version 1.1 (Rev. 22)
 * General: Added tlm models
 * Revision change in one or more subcores

2020.2.2:
 * Version 1.1 (Rev. 21)
 * No changes

2020.2.1:
 * Version 1.1 (Rev. 21)
 * No changes

2020.2:
 * Version 1.1 (Rev. 21)
 * Revision change in one or more subcores

2020.1.1:
 * Version 1.1 (Rev. 20)
 * No changes

2020.1:
 * Version 1.1 (Rev. 20)
 * Revision change in one or more subcores

2019.2.2:
 * Version 1.1 (Rev. 19)
 * No changes

2019.2.1:
 * Version 1.1 (Rev. 19)
 * No changes

2019.2:
 * Version 1.1 (Rev. 19)
 * General: Allow 1:1 converter mode (passthrough.)
 * Revision change in one or more subcores

2019.1.3:
 * Version 1.1 (Rev. 18)
 * No changes

2019.1.2:
 * Version 1.1 (Rev. 18)
 * No changes

2019.1.1:
 * Version 1.1 (Rev. 18)
 * No changes

2019.1:
 * Version 1.1 (Rev. 18)
 * Revision change in one or more subcores

2018.3.1:
 * Version 1.1 (Rev. 17)
 * No changes

2018.3:
 * Version 1.1 (Rev. 17)
 * General: Product Guide link updated.
 * Revision change in one or more subcores

2018.2:
 * Version 1.1 (Rev. 16)
 * Revision change in one or more subcores

2018.1:
 * Version 1.1 (Rev. 15)
 * General: Update internal register slice instantiation to tie-off unused input clock aclk2x.
 * General: Change fsm encoding and remove unnecessary combinatorial reset tie-off on output valid/ready signals.
 * General: Update initial values on register declarations to match reset values for reset-less operation.
 * Revision change in one or more subcores

2017.4:
 * Version 1.1 (Rev. 14)
 * Revision change in one or more subcores

2017.3:
 * Version 1.1 (Rev. 13)
 * Initializing Valid/Ready Outputs to zero before reset kicks in
 * Revision change in one or more subcores

2017.2:
 * Version 1.1 (Rev. 12)
 * Revision change in one or more subcores

2017.1:
 * Version 1.1 (Rev. 11)
 * Revision change in one or more subcores

2016.4:
 * Version 1.1 (Rev. 10)
 * Revision change in one or more subcores

2016.3:
 * Version 1.1 (Rev. 9)
 * Source HDL files are concatenated into a single file to speed up synthesis and simulation. No changes required by the user
 * Revision change in one or more subcores

2016.2:
 * Version 1.1 (Rev. 8)
 * Revision change in one or more subcores

2016.1:
 * Version 1.1 (Rev. 7)
 * Changes to HDL library management to support Vivado IP simulation library
 * Revision change in one or more subcores

2015.4.2:
 * Version 1.1 (Rev. 6)
 * No changes

2015.4.1:
 * Version 1.1 (Rev. 6)
 * No changes

2015.4:
 * Version 1.1 (Rev. 6)
 * Revision change in one or more subcores

2015.3:
 * Version 1.1 (Rev. 5)
 * IP revision number added to HDL module, library, and include file names, to support designs with both locked and upgraded IP instances
 * Configurations where there is non-multiple tdata width conversion (e.g., 2:3, 4:3, etc), no TKEEP and  no TID, TDEST or TLAST signals present were found to be adding the TKEEP signal with all bits tied to LOW to the output M_AXIS interface.  The TKEEP output can be ignored in this configuration. While the TKEEP is not needed, it has been kept to not break backwards compatibility and the vector is now being driven HIGH to produce a valid output.  Configurations that require an upsizer (more than 1 input transfer is accumulated into 1 or more output transfers) and have TID/TDEST/TLAST and no TKEEP, will still produce a TKEEP.  The TKEEP in this instance is not always tied HIGH and must be monitored if the input stream is not conditioned to ensure that TID/TDEST/TLAST do not toggle during accumulation.
 * Revision change in one or more subcores

2015.2.1:
 * Version 1.1 (Rev. 4)
 * No changes

2015.2:
 * Version 1.1 (Rev. 4)
 * No changes

2015.1:
 * Version 1.1 (Rev. 4)
 * The support status for Kintex UltraScale is changed from Pre-Production to Production.
 * Enabled out-of-context clock frequency setting by adding FREQ_HZ parameter to clock interface CLKIF

2014.4.1:
 * Version 1.1 (Rev. 3)
 * No changes

2014.4:
 * Version 1.1 (Rev. 3)
 * Architecture support updated

2014.3:
 * Version 1.1 (Rev. 2)
 * No changes

2014.2:
 * Version 1.1 (Rev. 2)
 * No changes

2014.1:
 * Version 1.1 (Rev. 2)
 * Internal device family name change, no functional changes

2013.4:
 * Version 1.1 (Rev. 1)
 * Kintex UltraScale Pre-Production support

2013.3:
 * Version 1.1
 * Added example design
 * Initial default value for maximum range of TUSER bits per num TDATA bytes changed from 32 to 2048 to correspond with a TDATA number of bytes of 2. Absolute TUSER width limit is 4096 bits wide.
 * Reduced warnings in synthesis and simulation

2013.2:
 * Version 1.0 (Rev. 1)
 * Architecture support updated

2013.1:
 * Version 1.0
 * Native Vivado Release
 * There have been no functional or interface changes to this IP.  The version number has changed to support unique versioning in Vivado starting with 2013.1.

(c) Copyright 2012 - 2021 Xilinx, Inc. All rights reserved.

This file contains confidential and proprietary information
of Xilinx, Inc. and is protected under U.S. and
international copyright and other intellectual property
laws.

DISCLAIMER
This disclaimer is not a license and does not grant any
rights to the materials distributed herewith. Except as
otherwise provided in a valid license issued to you by
Xilinx, and to the maximum extent permitted by applicable
law: (1) THESE MATERIALS ARE MADE AVAILABLE "AS IS" AND
WITH ALL FAULTS, AND XILINX HEREBY DISCLAIMS ALL WARRANTIES
AND CONDITIONS, EXPRESS, IMPLIED, OR STATUTORY, INCLUDING
BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, NON-
INFRINGEMENT, OR FITNESS FOR ANY PARTICULAR PURPOSE; and
(2) Xilinx shall not be liable (whether in contract or tort,
including negligence, or under any other theory of
liability) for any loss or damage of any kind or nature
related to, arising under or in connection with these
materials, including for any direct, or any indirect,
special, incidental, or consequential loss or damage
(including loss of data, profits, goodwill, or any type of
loss or damage suffered as a result of any action brought
by a third party) even if such damage or loss was
reasonably foreseeable or Xilinx had been advised of the
possibility of the same.

CRITICAL APPLICATIONS
Xilinx products are not designed or intended to be fail-
safe, or for use in any application requiring fail-safe
performance, such as life-support or safety devices or
systems, Class III medical devices, nuclear facilities,
applications related to the deployment of airbags, or any
other applications that could lead to death, personal
injury, or severe property or environmental damage
(individually and collectively, "Critical
Applications"). Customer assumes the sole risk and
liability of any use of Xilinx products in Critical
Applications, subject only to applicable laws and
regulations governing limitations on product liability.

THIS COPYRIGHT NOTICE AND DISCLAIMER MUST BE RETAINED AS
PART OF THIS FILE AT ALL TIMES.
