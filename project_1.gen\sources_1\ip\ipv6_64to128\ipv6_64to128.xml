<?xml version="1.0" encoding="UTF-8"?>
<spirit:component xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>xilinx.com</spirit:vendor>
  <spirit:library>customized_ip</spirit:library>
  <spirit:name>ipv6_64to128</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:busInterfaces>
    <spirit:busInterface>
      <spirit:name>S_AXIS</spirit:name>
      <spirit:displayName>S_AXIS</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="axis" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="axis_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axis_tvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axis_tready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TDATA</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axis_tdata</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TSTRB</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axis_tstrb</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TKEEP</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axis_tkeep</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TLAST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axis_tlast</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axis_tid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TDEST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axis_tdest</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TUSER</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s_axis_tuser</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>TDATA_NUM_BYTES</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS.TDATA_NUM_BYTES">8</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TDEST_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS.TDEST_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TID_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS.TID_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS.TUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TREADY</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS.HAS_TREADY">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TSTRB</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS.HAS_TSTRB">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TKEEP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS.HAS_TKEEP">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TLAST</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS.HAS_TLAST">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS.PHASE">0.0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS.CLK_DOMAIN"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>LAYERED_METADATA</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.S_AXIS.LAYERED_METADATA">undef</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.S_AXIS.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M_AXIS</spirit:name>
      <spirit:displayName>M_AXIS</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="axis" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="axis_rtl" spirit:version="1.0"/>
      <spirit:master/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_axis_tvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_axis_tready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TDATA</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_axis_tdata</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TSTRB</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_axis_tstrb</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TKEEP</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_axis_tkeep</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TLAST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_axis_tlast</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_axis_tid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TDEST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_axis_tdest</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>TUSER</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m_axis_tuser</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>TDATA_NUM_BYTES</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS.TDATA_NUM_BYTES">16</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TDEST_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS.TDEST_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TID_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS.TID_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>TUSER_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS.TUSER_WIDTH">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TREADY</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS.HAS_TREADY">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TSTRB</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS.HAS_TSTRB">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TKEEP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS.HAS_TKEEP">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>HAS_TLAST</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS.HAS_TLAST">1</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS.FREQ_HZ">100000000</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS.PHASE">0.0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS.CLK_DOMAIN"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>LAYERED_METADATA</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.M_AXIS.LAYERED_METADATA">undef</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.M_AXIS.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>RSTIF</spirit:name>
      <spirit:displayName>RSTIF</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>aresetn</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.RSTIF.POLARITY">ACTIVE_LOW</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.RSTIF.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>CLKIF</spirit:name>
      <spirit:displayName>CLKIF</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>aclk</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>FREQ_HZ</spirit:name>
          <spirit:displayName>aclk frequency</spirit:displayName>
          <spirit:description>aclk frequency</spirit:description>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.CLKIF.FREQ_HZ" spirit:minimum="1" spirit:maximum="1000000000" spirit:rangeType="long">10000000</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>FREQ_TOLERANCE_HZ</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLKIF.FREQ_TOLERANCE_HZ">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>PHASE</spirit:name>
          <spirit:value spirit:format="float" spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLKIF.PHASE">0.0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>CLK_DOMAIN</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLKIF.CLK_DOMAIN"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_BUSIF</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLKIF.ASSOCIATED_BUSIF"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_RESET</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLKIF.ASSOCIATED_RESET"/>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>INSERT_VIP</spirit:name>
          <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="BUSIFPARAM_VALUE.CLKIF.INSERT_VIP">0</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>simulation.rtl</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>CLKENIF</spirit:name>
      <spirit:displayName>CLKENIF</spirit:displayName>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clockenable" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clockenable_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CE</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>aclken</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:resolve="generated" spirit:id="BUSIFPARAM_VALUE.CLKENIF.POLARITY">ACTIVE_LOW</spirit:value>
          <spirit:vendorExtensions>
            <xilinx:parameterInfo>
              <xilinx:parameterUsage>none</xilinx:parameterUsage>
            </xilinx:parameterInfo>
          </spirit:vendorExtensions>
        </spirit:parameter>
      </spirit:parameters>
      <spirit:vendorExtensions>
        <xilinx:busInterfaceInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="BUSIF_ENABLEMENT.CLKENIF" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.HAS_ACLKEN&apos;)) = 1)">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:busInterfaceInfo>
      </spirit:vendorExtensions>
    </spirit:busInterface>
  </spirit:busInterfaces>
  <spirit:model>
    <spirit:views>
      <spirit:view>
        <spirit:name>xilinx_veriloginstantiationtemplate</spirit:name>
        <spirit:displayName>Verilog Instantiation Template</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:synthesis.template</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_veriloginstantiationtemplate_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Fri Jul 11 02:13:01 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:b008023e</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogsynthesis</spirit:name>
        <spirit:displayName>Verilog Synthesis</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:synthesis</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>axis_dwidth_converter_v1_1_23_axis_dwidth_converter</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsynthesis_xilinx_com_ip_axis_infrastructure_1_1__ref_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsynthesis_xilinx_com_ip_axis_register_slice_1_1__ref_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsynthesis_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Fri Jul 11 02:16:24 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:b008023e</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_synthesisconstraints</spirit:name>
        <spirit:displayName>Synthesis Constraints</spirit:displayName>
        <spirit:envIdentifier>:vivado.xilinx.com:synthesis.constraints</spirit:envIdentifier>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_synthesisconstraints_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Fri Jul 11 02:16:24 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:b008023e</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogsynthesiswrapper</spirit:name>
        <spirit:displayName>Verilog Synthesis Wrapper</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:synthesis.wrapper</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>ipv6_64to128</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsynthesiswrapper_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Fri Jul 11 02:16:24 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:b008023e</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogbehavioralsimulation</spirit:name>
        <spirit:displayName>Verilog Simulation</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:simulation</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>axis_dwidth_converter_v1_1_23_axis_dwidth_converter</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogbehavioralsimulation_xilinx_com_ip_axis_infrastructure_1_1__ref_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogbehavioralsimulation_xilinx_com_ip_axis_register_slice_1_1__ref_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogbehavioralsimulation_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Fri Jul 11 02:16:24 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:805aa7c8</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogsimulationwrapper</spirit:name>
        <spirit:displayName>Verilog Simulation Wrapper</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:simulation.wrapper</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>ipv6_64to128</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsimulationwrapper_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Fri Jul 11 02:16:24 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:805aa7c8</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_versioninformation</spirit:name>
        <spirit:displayName>Version Information</spirit:displayName>
        <spirit:envIdentifier>:vivado.xilinx.com:docs.versioninfo</spirit:envIdentifier>
        <spirit:modelName>axis_dwidth_converter_v1_1_23_axis_dwidth_converter</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_versioninformation_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Fri Jul 11 02:16:24 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:b008023e</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_externalfiles</spirit:name>
        <spirit:displayName>External Files</spirit:displayName>
        <spirit:envIdentifier>:vivado.xilinx.com:external.files</spirit:envIdentifier>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_externalfiles_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>GENtimestamp</spirit:name>
            <spirit:value>Fri Jul 11 02:17:24 UTC 2025</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>outputProductCRC</spirit:name>
            <spirit:value>9:b008023e</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
    </spirit:views>
    <spirit:ports>
      <spirit:port>
        <spirit:name>aclk</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>aresetn</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>aclken</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:resolve="dependent" spirit:bitStringLength="1">0x1</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.aclken" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.HAS_ACLKEN&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axis_tvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0x0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axis_tready</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axis_tready" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.HAS_TREADY&apos;)) = 1)">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axis_tdata</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_S_AXIS_TDATA_WIDTH&apos;)) - 1)">63</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:resolve="dependent" spirit:dependency="{(spirit:decode(id(&apos;MODELPARAM_VALUE.C_S_AXIS_TDATA_WIDTH&apos;))){0}}" spirit:bitStringLength="8">0x0000000000000000</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axis_tstrb</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_S_AXIS_TDATA_WIDTH&apos;)) / 8) - 1)">7</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:resolve="dependent" spirit:dependency="{((spirit:decode(id(&apos;MODELPARAM_VALUE.C_S_AXIS_TDATA_WIDTH&apos;)) / 8)){1}}" spirit:bitStringLength="1">0xFF</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axis_tstrb" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.HAS_TSTRB&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axis_tkeep</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_S_AXIS_TDATA_WIDTH&apos;)) / 8) - 1)">7</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:resolve="dependent" spirit:dependency="{((spirit:decode(id(&apos;MODELPARAM_VALUE.C_S_AXIS_TDATA_WIDTH&apos;)) / 8)){1}}" spirit:bitStringLength="1">0xFF</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axis_tkeep" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.HAS_TKEEP&apos;)) = 1)">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axis_tlast</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0x1</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axis_tlast" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.HAS_TLAST&apos;)) = 1)">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axis_tid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXIS_TID_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:resolve="dependent" spirit:dependency="{(spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXIS_TID_WIDTH&apos;))){0}}" spirit:bitStringLength="1">0x0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axis_tid" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.TID_WIDTH&apos;)) > 0)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axis_tdest</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXIS_TDEST_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:resolve="dependent" spirit:dependency="{(spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXIS_TDEST_WIDTH&apos;))){0}}" spirit:bitStringLength="1">0x0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axis_tdest" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.TDEST_WIDTH&apos;)) > 0)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>s_axis_tuser</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_S_AXIS_TUSER_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:resolve="dependent" spirit:dependency="{(spirit:decode(id(&apos;MODELPARAM_VALUE.C_S_AXIS_TUSER_WIDTH&apos;))){0}}" spirit:bitStringLength="1">0x0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.s_axis_tuser" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.TUSER_BITS_PER_BYTE&apos;)) > 0)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_axis_tvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_axis_tready</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="bitString" spirit:bitStringLength="1">0x1</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m_axis_tready" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.HAS_TREADY&apos;)) = 1)">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_axis_tdata</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M_AXIS_TDATA_WIDTH&apos;)) - 1)">127</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_axis_tstrb</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_M_AXIS_TDATA_WIDTH&apos;)) / 8) - 1)">15</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m_axis_tstrb" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.HAS_TSTRB&apos;)) = 1)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_axis_tkeep</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_M_AXIS_TDATA_WIDTH&apos;)) / 8) - 1)">15</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m_axis_tkeep" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.HAS_TKEEP&apos;)) = 1) || (spirit:decode(id(&apos;PARAM_VALUE.HAS_MI_TKEEP&apos;)) = 1) ">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_axis_tlast</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m_axis_tlast" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.HAS_TLAST&apos;)) = 1)">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_axis_tid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXIS_TID_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m_axis_tid" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.TID_WIDTH&apos;)) > 0)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_axis_tdest</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_AXIS_TDEST_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m_axis_tdest" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.TDEST_WIDTH&apos;)) > 0)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m_axis_tuser</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M_AXIS_TUSER_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m_axis_tuser" xilinx:dependency="(spirit:decode(id(&apos;PARAM_VALUE.TUSER_BITS_PER_BYTE&apos;)) > 0)">false</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
    </spirit:ports>
    <spirit:modelParameters>
      <spirit:modelParameter xsi:type="spirit:nameValueTypeType" spirit:dataType="string">
        <spirit:name>C_FAMILY</spirit:name>
        <spirit:value spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_FAMILY">kintex7</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_S_AXIS_TDATA_WIDTH</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_S_AXIS_TDATA_WIDTH">64</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_M_AXIS_TDATA_WIDTH</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M_AXIS_TDATA_WIDTH">128</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_AXIS_TID_WIDTH</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_AXIS_TID_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_AXIS_TDEST_WIDTH</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_AXIS_TDEST_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_S_AXIS_TUSER_WIDTH</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_S_AXIS_TUSER_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_M_AXIS_TUSER_WIDTH</spirit:name>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M_AXIS_TUSER_WIDTH">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_AXIS_SIGNAL_SET</spirit:name>
        <spirit:value spirit:format="bitString" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_AXIS_SIGNAL_SET" spirit:bitStringLength="32">0b00000000000000000000000000011011</spirit:value>
      </spirit:modelParameter>
    </spirit:modelParameters>
  </spirit:model>
  <spirit:choices>
    <spirit:choice>
      <spirit:name>choice_pairs_37189c7b</spirit:name>
      <spirit:enumeration spirit:text="No">0</spirit:enumeration>
      <spirit:enumeration spirit:text="Yes">1</spirit:enumeration>
    </spirit:choice>
  </spirit:choices>
  <spirit:fileSets>
    <spirit:fileSet>
      <spirit:name>xilinx_veriloginstantiationtemplate_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>ipv6_64to128.vho</spirit:name>
        <spirit:userFileType>vhdlTemplate</spirit:userFileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>ipv6_64to128.veo</spirit:name>
        <spirit:userFileType>verilogTemplate</spirit:userFileType>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsynthesis_xilinx_com_ip_axis_infrastructure_1_1__ref_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>hdl/axis_infrastructure_v1_1_0.vh</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>axis_infrastructure_v1_1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>hdl/axis_infrastructure_v1_1_vl_rfs.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:logicalName>axis_infrastructure_v1_1_0</spirit:logicalName>
      </spirit:file>
      <spirit:vendorExtensions>
        <xilinx:subCoreRef>
          <xilinx:componentRef xilinx:vendor="xilinx.com" xilinx:library="ip" xilinx:name="axis_infrastructure" xilinx:version="1.1" xilinx:isGenerated="true" xilinx:checksum="ea44fec2">
            <xilinx:mode xilinx:name="copy_mode"/>
          </xilinx:componentRef>
        </xilinx:subCoreRef>
      </spirit:vendorExtensions>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsynthesis_xilinx_com_ip_axis_register_slice_1_1__ref_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>hdl/axis_register_slice_v1_1_vl_rfs.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:logicalName>axis_register_slice_v1_1_24</spirit:logicalName>
      </spirit:file>
      <spirit:vendorExtensions>
        <xilinx:subCoreRef>
          <xilinx:componentRef xilinx:vendor="xilinx.com" xilinx:library="ip" xilinx:name="axis_register_slice" xilinx:version="1.1" xilinx:isGenerated="true" xilinx:checksum="c321485d">
            <xilinx:mode xilinx:name="copy_mode"/>
          </xilinx:componentRef>
        </xilinx:subCoreRef>
      </spirit:vendorExtensions>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsynthesis_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>hdl/axis_dwidth_converter_v1_1_vl_rfs.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:logicalName>axis_dwidth_converter_v1_1_23</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_synthesisconstraints_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>ipv6_64to128_ooc.xdc</spirit:name>
        <spirit:userFileType>xdc</spirit:userFileType>
        <spirit:userFileType>USED_IN_implementation</spirit:userFileType>
        <spirit:userFileType>USED_IN_out_of_context</spirit:userFileType>
        <spirit:userFileType>USED_IN_synthesis</spirit:userFileType>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsynthesiswrapper_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>synth/ipv6_64to128.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:logicalName>xil_defaultlib</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogbehavioralsimulation_xilinx_com_ip_axis_infrastructure_1_1__ref_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>hdl/axis_infrastructure_v1_1_0.vh</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:isIncludeFile>true</spirit:isIncludeFile>
        <spirit:logicalName>axis_infrastructure_v1_1_0</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>hdl/axis_infrastructure_v1_1_vl_rfs.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:logicalName>axis_infrastructure_v1_1_0</spirit:logicalName>
      </spirit:file>
      <spirit:vendorExtensions>
        <xilinx:subCoreRef>
          <xilinx:componentRef xilinx:vendor="xilinx.com" xilinx:library="ip" xilinx:name="axis_infrastructure" xilinx:version="1.1" xilinx:isGenerated="true" xilinx:checksum="ea44fec2">
            <xilinx:mode xilinx:name="copy_mode"/>
          </xilinx:componentRef>
        </xilinx:subCoreRef>
      </spirit:vendorExtensions>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogbehavioralsimulation_xilinx_com_ip_axis_register_slice_1_1__ref_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>hdl/axis_register_slice_v1_1_vl_rfs.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:logicalName>axis_register_slice_v1_1_24</spirit:logicalName>
      </spirit:file>
      <spirit:vendorExtensions>
        <xilinx:subCoreRef>
          <xilinx:componentRef xilinx:vendor="xilinx.com" xilinx:library="ip" xilinx:name="axis_register_slice" xilinx:version="1.1" xilinx:isGenerated="true" xilinx:checksum="c321485d">
            <xilinx:mode xilinx:name="copy_mode"/>
          </xilinx:componentRef>
        </xilinx:subCoreRef>
      </spirit:vendorExtensions>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogbehavioralsimulation_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>hdl/axis_dwidth_converter_v1_1_vl_rfs.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_ipstatic</spirit:userFileType>
        <spirit:logicalName>axis_dwidth_converter_v1_1_23</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsimulationwrapper_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>sim/ipv6_64to128.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:logicalName>xil_defaultlib</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_versioninformation_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>doc/axis_dwidth_converter_v1_1_changelog.txt</spirit:name>
        <spirit:userFileType>text</spirit:userFileType>
        <spirit:logicalName>axis_dwidth_converter_v1_1_23</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_externalfiles_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>ipv6_64to128.dcp</spirit:name>
        <spirit:userFileType>dcp</spirit:userFileType>
        <spirit:userFileType>USED_IN_implementation</spirit:userFileType>
        <spirit:userFileType>USED_IN_synthesis</spirit:userFileType>
        <spirit:logicalName>xil_defaultlib</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>ipv6_64to128_stub.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_synth_blackbox_stub</spirit:userFileType>
        <spirit:logicalName>xil_defaultlib</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>ipv6_64to128_stub.vhdl</spirit:name>
        <spirit:fileType>vhdlSource</spirit:fileType>
        <spirit:userFileType>USED_IN_synth_blackbox_stub</spirit:userFileType>
        <spirit:logicalName>xil_defaultlib</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>ipv6_64to128_sim_netlist.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:userFileType>USED_IN_simulation</spirit:userFileType>
        <spirit:userFileType>USED_IN_single_language</spirit:userFileType>
        <spirit:logicalName>xil_defaultlib</spirit:logicalName>
      </spirit:file>
      <spirit:file>
        <spirit:name>ipv6_64to128_sim_netlist.vhdl</spirit:name>
        <spirit:fileType>vhdlSource</spirit:fileType>
        <spirit:userFileType>USED_IN_simulation</spirit:userFileType>
        <spirit:userFileType>USED_IN_single_language</spirit:userFileType>
        <spirit:logicalName>xil_defaultlib</spirit:logicalName>
      </spirit:file>
    </spirit:fileSet>
  </spirit:fileSets>
  <spirit:description>The AXI4-Stream Data Widith Converter IP provides the infrastructure to change the data path width between a AXI4-Stream master and slave.</spirit:description>
  <spirit:parameters>
    <spirit:parameter>
      <spirit:name>S_TDATA_NUM_BYTES</spirit:name>
      <spirit:displayName>Slave Interface TDATA Width (bytes)</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.S_TDATA_NUM_BYTES" spirit:order="2" spirit:minimum="1" spirit:maximum="512" spirit:rangeType="long">8</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.S_TDATA_NUM_BYTES">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>M_TDATA_NUM_BYTES</spirit:name>
      <spirit:displayName>Master Interface TDATA Width (bytes)</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.M_TDATA_NUM_BYTES" spirit:order="3" spirit:minimum="1" spirit:maximum="512" spirit:rangeType="long">16</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.M_TDATA_NUM_BYTES">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>TID_WIDTH</spirit:name>
      <spirit:displayName>TID Width (bits)</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.TID_WIDTH" spirit:order="4" spirit:minimum="0" spirit:maximum="32" spirit:rangeType="long">0</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.TID_WIDTH">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>TDEST_WIDTH</spirit:name>
      <spirit:displayName>TDEST Width (bits)</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.TDEST_WIDTH" spirit:order="5" spirit:minimum="0" spirit:maximum="32" spirit:rangeType="long">0</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.TDEST_WIDTH">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>TUSER_BITS_PER_BYTE</spirit:name>
      <spirit:displayName>TUSER bits per byte</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.TUSER_BITS_PER_BYTE" spirit:order="6" spirit:minimum="0" spirit:maximum="256" spirit:rangeType="long">0</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.TUSER_BITS_PER_BYTE">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>HAS_TREADY</spirit:name>
      <spirit:displayName>Enable TREADY</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.HAS_TREADY" spirit:choiceRef="choice_pairs_37189c7b" spirit:order="7">1</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.HAS_TREADY">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>HAS_TLAST</spirit:name>
      <spirit:displayName>Enable TLAST</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.HAS_TLAST" spirit:choiceRef="choice_pairs_37189c7b" spirit:order="8">1</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.HAS_TLAST">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>HAS_TSTRB</spirit:name>
      <spirit:displayName>Enable TSTRB</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.HAS_TSTRB" spirit:choiceRef="choice_pairs_37189c7b" spirit:order="9">0</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.HAS_TSTRB">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>HAS_TKEEP</spirit:name>
      <spirit:displayName>Enable TKEEP</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.HAS_TKEEP" spirit:choiceRef="choice_pairs_37189c7b" spirit:order="10">1</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.HAS_TKEEP">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>HAS_ACLKEN</spirit:name>
      <spirit:displayName>Enable ACLKEN</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.HAS_ACLKEN" spirit:choiceRef="choice_pairs_37189c7b" spirit:order="11">0</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.HAS_ACLKEN">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>HAS_MI_TKEEP</spirit:name>
      <spirit:displayName>Enable MI TKEEP</spirit:displayName>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.HAS_MI_TKEEP" spirit:choiceRef="choice_pairs_37189c7b" spirit:order="12">1</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PARAM_ENABLEMENT.HAS_MI_TKEEP">true</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Component_Name</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Component_Name" spirit:order="1">ipv6_64to128</spirit:value>
    </spirit:parameter>
  </spirit:parameters>
  <spirit:vendorExtensions>
    <xilinx:coreExtensions>
      <xilinx:displayName>AXI4-Stream Data Width Converter</xilinx:displayName>
      <xilinx:xpmLibraries>
        <xilinx:xpmLibrary>XPM_CDC</xilinx:xpmLibrary>
        <xilinx:xpmLibrary>XPM_FIFO</xilinx:xpmLibrary>
      </xilinx:xpmLibraries>
      <xilinx:coreRevision>23</xilinx:coreRevision>
      <xilinx:configElementInfos>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_AXIS.HAS_TKEEP" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_AXIS.HAS_TLAST" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.M_AXIS.TDATA_NUM_BYTES" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXIS.HAS_TKEEP" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXIS.HAS_TLAST" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="BUSIFPARAM_VALUE.S_AXIS.TDATA_NUM_BYTES" xilinx:valueSource="auto"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.HAS_MI_TKEEP" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.HAS_TKEEP" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.HAS_TLAST" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.M_TDATA_NUM_BYTES" xilinx:valueSource="user"/>
        <xilinx:configElementInfo xilinx:referenceId="PARAM_VALUE.S_TDATA_NUM_BYTES" xilinx:valueSource="user"/>
      </xilinx:configElementInfos>
    </xilinx:coreExtensions>
    <xilinx:packagingInfo>
      <xilinx:xilinxVersion>2021.1</xilinx:xilinxVersion>
      <xilinx:checksum xilinx:scope="busInterfaces" xilinx:value="197e0bf8"/>
      <xilinx:checksum xilinx:scope="fileGroups" xilinx:value="d425b0bd"/>
      <xilinx:checksum xilinx:scope="ports" xilinx:value="93ee4749"/>
      <xilinx:checksum xilinx:scope="hdlParameters" xilinx:value="12203d91"/>
      <xilinx:checksum xilinx:scope="parameters" xilinx:value="324044e2"/>
    </xilinx:packagingInfo>
  </spirit:vendorExtensions>
</spirit:component>
